<!--
 * @Author: WDD
 * @Date: 2025-05-07 15:14:10
 * @LastEditors: WDD
 * @LastEditTime: 2025-05-20 10:26:25
 * @Description: file content
-->
## 摘要
2023年4月，为提升企业综合竞争力，我司正式启动了以制造执行系统（MES）为基石的信息化与数智化转型项目。作为系统架构师，我主导了该项目的整体架构规划和实施。本文以此项目为蓝本，深入剖析了在项目设计与执行过程中所遇到的挑战及如何采用架构风格进行解决。在系统架构整体设计上，我们采用云原生架构风格，依据业务领域边界将其细分为十大微服务模块并实现服务的弹性伸缩和自动化部署；为保障系统的安全性，采用数据流风格对http请求进行鉴权操作；基于虚拟机风格实现低代码开发平台，提高系统的可修改性。系统于2023年10月成功上线，并逐步覆盖至海内外8个生产基地，成效斐然。

## 正文

在制造业数字化转型的浪潮下，某省2022年工业互联网发展报告指出，73%的制造业因传统MES系统架构僵化，跨厂区协同效率低于行业平均水平。这些系统多采用单体架构，业务扩展时扩容成本高，硬件投入占比达42%，且难以适应柔性生产需求，导致信息孤岛、系统整合困难，阻碍企业数字化升级。

为解决生产数据实时处理能力不足、多产区协同效率低下等问题，我司于2023年4月正式启动了新一代云原生MES系统建设，旨在通过架构革新实现制造执行全流程的敏捷响应。项目采用三阶段迭代开发模式，首次预算800万，覆盖1个生产基地，5个产区，接入1000余台工业设备。MES系统是生产管理的核心工具，具有实时数据采集、生产进度监控、动态调度排程、质量控制和物料追溯等功能，可打通企业计划层与生产现场的信息壁垒，实现生产透明化与柔性化。作为架构师，我主导设计并实施云原生架构，完成10大微服务模块解耦，实现系统99.9999%高可用保障。截至2025年3月，系统已上线10个微服务模块，单生产基地日均产量达200MW，跨产区协同效率提升63%。

架构风格是描述某一特定系统所采用的组织方式和惯用手段，目前常用的架构风格包括：数据流风格、调用/返回风格、虚拟机风格、独立构件风格、以数据为中心的风格和云原生风格。其中数据流风格中包含批处理风格和管道-过滤器风格，这种风格的特点是处理流程中前一步的结果是后一步的输入，一般无需人员干预；调用/返回风格的子风格有主程序/子程序风格、面向对象风格和分层架构风格，它一般表示通过边界定义划分模块和层次结构，实现功能解耦，提升可扩展性；虚拟机风格分为解释器风格和以规则为中心的风格，它通常支持对信息按照约定的规则进行处理而不依赖于外部环境；独立构件风格包括进程通信、隐式调用等子风格，表示构件之间相互独立，通过事件注册表或消息队列构件进行交互，实现业务的解耦；以数据为中心的风格通过数据对功能进行解耦和扩展，它包括数据库风格和黑板风格，黑板风格一般用在处理数据格式比较复杂的系统中，例如图像识别、语音识别等；云原生风格是一种基于云原生技术通过将非业务功能从业务功能中剥离的架构设计方法，包括服务化、自动化、弹性和可观测性等设计原则。

本文将聚焦于架构风格在MES系统中的应用，分别从云原生架构风格、数据流风格和虚拟机风格等几个方面，深入剖析架构设计与执行过程中所遇到的问题和解决策略。

1.	云原生架构风格
在传统的单体MES架构中，模块之间相互耦合，难以扩展、测试和维护，通常一次需求变更会要求做一次回归测试，耗时2人天，部署一次系统则耗时2小时，而回滚操作则更是灾难性的，部署人员需要将备份的原程序文件进行覆盖，严重影响项目的迭代速度和响应能力，影响生产效率。因此，我们基于业务领域边界，将MES系统细分为计划和调度、工艺管理、质量管理、生产管理、设备管理、物料管理、用户管理、数据分析、日志追溯和系统集成等十大微服务模块，实现服务模块化和解耦；以Kubernetes为容器编排中枢，实现服务的弹性扩缩容；依托Azure DevOps平台，搭建CI/CD流水线，实现自动化测试和持续交付；基于Nacos实现服务发现和配置管理；通过API网关和RabbitMQ实现服务间通信；通过Dotnet Core CAP实现分布式事务；通过Skywalking实现日志采集，保障架构的可观测性。从单体架构到云原生架构的蜕变，我们实现了99.9999%的系统可用性、关键接口TP99<300ms的高性能和需求交付时间<3天的高可修改性。
2.	数据流风格
在MES系统中，一个http请求在进行业务访问和操作之前通常需要进行很多处理，包括对请求者进行识别判断是否是合法用户、判断请求者是否有该接口的请求权限、请求的数据是否满足接口参数格式、对请求进行熔断和限流；同时在返回结果时也需要进行一些额外操作，例如调整结果为统一格式、将请求保存日志、调用RabbitMQ推送结果到其他服务等。通过对架构风格的特点和优缺点进行对比，我们选择数据流风格进行这部分功能的实现。当用户登陆成功后，用户管理服务会生成一个JWT Token并返回给当前的前端页面或其他系统；前端页面或其他系统访问业务接口时，必须将JWT Token添加到http Header中；当请求到达MES系统时，Filter过滤器会先检查Token，从Redis中进行查询该Token所对应的用户及其权限列表；如果未查询到，则表示用户不存在或登陆已过期，并返回报错；如果查询到但所拥有的权限不包括当前访问的接口，则返回未授权的响应码401；如果请求者是合法用户且已授权，才允许该请求进入业务接口并返回接口请求结果。通过数据流风格我们能轻松保障系统的安全性，阻止非法用户的访问和操作。
3.	虚拟机风格
在质量管理、工艺管理、设备管理和物料管理等微服务模块中，会普遍用到数据的查看、定义和维护功能，而这些功能通常是对一张或多张数据库表的CRUD操作，且前端页面的布局和后端业务代码的结构层次均一致，这些重复操作占用了大量的开发时间，严重影响开发效率。针对这一问题，我们采用虚拟机架构风格设计了低代码开发平台。在该平台页面中，开发人员可以通过事先创建好的数据库表自动生成预设的前端页面和后端服务代码，必要的时候还允许开发人员对页面中的组件进行拖拉操作，从而修改页面显示样式；点击保存后，修改好的前端页面会以Json的形式持久化到数据库中，后端服务则会生成相应的接口和服务文件；通过菜单管理将该页面绑定到菜单上；当用户点击菜单查看该功能页面时，首先会请求后端服务获取页面所需的Json字符串；通过解释器将Json字符串转换成页面上的组件并显示；用户点击页面上的组件进行查看和操作数据。虚拟机架构风格的应用极大的减少了重复的开发工作，让之前5人天的开发测试工作缩短到2人天，提高了系统的可修改性和可扩展性。

系统于2023年10月成功上线，并取得了令人瞩目的成效：部署时间由原来的2小时大幅缩短至8分钟，硬件投入成本减少50%，服务响应速度提升60%，服务高可用性高达99.9999%。截至目前，系统已推广至海内外8个生产基地，实现了广泛的业务覆盖。架构风格在MES系统的实践表明，通过，可显著提高系统的性能、可用性、可扩展性和可修改性。

后续，我们持续对系统进行优化升级，例如，使用文生SQL技术革新数据分析模块，用户可通过自然语音提问自动生成查询SQL语句，极大提高查询便捷性和报表生成效率，不过，该功能在索引利用于与大小表连接顺序方面仍有优化空间。同时，利用深度学习结合图形分析技术，对不良品瑕疵点进行精准标注，有效减轻了质检人员的工作负担，提高了工作效率。展望未来，我们计划进一步探索AI技术在云原生架构中的应用潜力，研究自然语言生成前端界面和业务代码的可能性，以期待持续优化系统架构，提升开发效率与系统性能，为企业数字化转型注入更强动力。
