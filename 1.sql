DROP TABLE XBC.dbo.SYS_USER_WORKSTATIONCARD;

CREATE TABLE XBC.dbo.SYS_USER_WORKSTATIONCARD (
	SYS_USER_WORKSTATIONCARD_ID int IDENTITY(1,1) NOT NULL,
	<PERSON>ER_<PERSON> int NULL,
	SYS_WOR<PERSON><PERSON>TIONCARD_ID int NULL,
	P<PERSON><PERSON>ION_X int NULL,
	POS<PERSON>ION_Y int NULL,
	WIDTH int NULL,
	HEIGHT int NULL,
	COLOR varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
	TITLE varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
	CREATEDATE datetime NULL,
	CREATOR varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
	M<PERSON>IFYDATE datetime NULL,
	M<PERSON><PERSON>IE<PERSON> varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
	PAGESIZE int NULL,
	DEFAULTTAB varchar(50) COLLATE Chinese_PRC_CI_AS NULL,
	CONSTRAINT PK__SYS_USER_WORKSTATIONCARD PRIMARY KEY (SYS_USER_WORKSTATIONCARD_ID)
);