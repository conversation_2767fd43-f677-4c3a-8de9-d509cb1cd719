<!--
 * @Author: WDD
 * @Date: 2025-04-13 20:50:30
 * @LastEditors: WDD
 * @LastEditTime: 2025-04-16 10:41:02
 * @Description: file content
-->
## 简介
    2023年4月，为增加企业核心竞争力，我司启动了以制造执行系统（MES）为基石的信息化数智化转型项目。作为项目架构师，我主导了系统的架构设计工作。本文以该项目为蓝本，深入探讨了云原生架构在MES系统中的应用，以及在项目设计与实现过程中遇到的挑战及相应应对措施。在系统架构设计上，我们依据业务领取边界，将系统细分为十大微服务模块，实现服务的模块和解耦；以Kubernetes为容器编排中枢，确保了系统的高可用和弹性伸缩能力；依托Azure DevOps平台，构建了高校的CI/CD流水线，实现了系统的自动化测试与持续部署，严格遵循了云原生架构的四类设计原则。系统于2024年10月成功上线，并逐步覆盖至海内外8个生产基地，成效斐然。

## 正文
    随着企业规模的不断扩大，工厂产量的不断提升，用户对制造执行系统（以下简称为“MES系统”）的非功能需求越来越激烈，传统单体架构MES系统暴露出诸多问题：系统耦合度高、扩展性差、单点故障频发、运维成本高昂（年均硬件投入增长37%）、跨厂区协同效率低下。为解决这些问题，同时为响应某省制造业数字化转型的号召，提高企业核心竞争力，2023年4月，我所在公司启动了以MES系统为核心的信息化数智化建设。作为项目架构师，我主导了系统的架构需求分析、架构设计、架构文档化和架构实现工作，并将系统架构设计为云原生架构。
    
    云原生架构（Cloud Native Architecture）是一种基于云原生技术通过将非业务功能从业务功能中剥离的架构设计方法，旨在实现系统的高可用性、高扩展性、高弹性和高可观测性。云原生架构的服务化设计要求将系统拆分为多个独立的微服务，每个服务都应该是自治的、松耦合的，能够独立部署和扩展，服务之间通过轻量级协议通信，具有明确的服务边界和职责；弹性设计强调系统能够根据负载动态调整资源配置，通过自动化的资源管理、负载均衡和故障转移等机制，实现服务的弹性伸缩和容错，确保服务的持续可用；可观测性设计要求系统能够提供充分的监控、日志和追踪能力，通过收集和分析这些数据，运维团队能够快速识别和定位问题，了解系统的运行状态和性能表现；自动化设计强调通过自动化工具和流程，实现系统的自动化部署、配置管理、持续集成和持续交付，这不仅能提高系统的交付效率，还能降低人为错误，保证部署的一致性。云原生架构通过引入容器化、微服务、DevOps、持续集成和持续交付等技术，以实现系统的高弹性和自动化；通过引入Skywalking实现链路追踪，通过ELK（Elasticsearch、Logstash、Kibana）实现日志的收集、存储、查询和分析，实现系统的可观测性。
    
    本文将着重介绍云原生架构在MES系统中的应用，并围绕服务化、弹性和自动化等四类设计原则，论述了在项目设计与实现过程中遇到的困难与解决方法。

    1. 服务化

    基于领域驱动设计理念，MES系统按领域边界拆分为十大微服务模块：生产计划与调度、工艺管理、生产管理、质量管理、设备管理、物料管理、数据分析、用户管理、日志追溯和系统集成。生产计划与调度用于实现生产排程、任务调度和进度追踪等功能；工艺管理用于管理工艺流程，包括工艺路线定义、工艺参数定义、工艺文件管理和工艺版本管理等；生产管理用于采集生产数据，实时监控生产过程；质量管理实现质量检验计划制定、质量检测、质量分析和不良品管理等功能；设备管理主要有设备状态监控、设备稼动率分析等功能；数据分析用于对生产、质量、设备、物料等数据进行分析，提供决策支持；日志追溯用于实现批次、物料、设备和质量的追溯等功能；系统集成用于实现系统与外部系统的集成。每个微服务模块分配给不同的开发小组进行开发，支持使用不同的技术栈和数据库，服务之间通过API网关进行数据交换，实现数据的解耦和独立性。但是，微服务架构也带来了服务间通信的复杂性和数据一致性的挑战。为了解决这些问题，我们引入了Ocelot API网关、DotNetCore.CAP分布式事务和RabbitMQ消息队列等技术。

    2. 弹性

    MES系统的平台保障服务以Kubernetes为核心，集成了Nacos、RabbitMQ等中间件，保障系统的高可用性。Kubernetes作为容器编排平台，提供了自动化部署、扩展和管理容器化应用的能力。它支持根据CPU利用率自动对单个服务进行扩缩容。在我所在公司中，每个月底生产部门都会对库存进行盘点，需要频繁地查询数据分析模块，在未使用云原生架构之前，系统经常会因为资源耗竭而出现单点故障，需要花费大量人力去修复，而采用了Kubernetes进行容器化部署之后，数据分析服务会根据CPU利用率自动进行扩容，满足激增的查询需求。但在该项目中，数据库我们使用的是Orcle数据库，部署在虚拟机中，这成为了系统的性能瓶颈。因此我采用了前端本地缓存、API网关缓存、后端业务服务缓存、Redis分布式缓存等多级缓存和数据库读写分离的方式，减少数据库压力。 

    3. 可观测性

    MES系统通过引入Skywalking实现链路追踪，通过ELK实现日志的收集、存储、查询和分析，保障了系统的可观测性。Skywalking支持从应用视图了解服务之间的调用关系，从链路视图了解请求的链路信息，从告警视图了解服务、服务器、应用的性能波动。ELK支持从日志中了解Sql语句的执行效率和API接口的响应时间，分析出故障原因。通过引入Skywalking和ELK，我们可以快速定位问题，了解服务运行状态，提高系统的可观测性。但分布式环境下日志分散，故障定位耗时较长，因此我们为所有服务注入唯一Trace ID，实现跨服务日志关联，接入了邮件告警和飞书告警，及时了解系统异常。此外，我们开发了日志追溯模块，支持用户查看批次状态变化、设备运行状态变化和生产质量水位线。

    4. 自动化

    MES系统基于Azure DevOps建立CI/CD流水线，实现系统的自动化部署和管理。CI/CD流水线支持自动化的代码构建、测试、部署和回滚，提高了系统的交付效率和质量。此外，流水线还支持自动化的监控和报警，提高了系统的可用性和可靠性。同时得益于Kubernetes的灰度发布，我们可以对部分功能进行上线和用户测试，极大的降低了风险，保证了系统稳定性和可靠性。在部署的过程中，我们遇到测试环境与正式环境配置不一致的问题。为了解决这个问题，我们将代码部署分为了几个步骤：1. 使用Nacos作为配置管理中心，将不同环境的配置信息存储在不同的GroupID中；2. 根据代码分支调用不同的部署管道，部署管道对应不同的运行环境；3. 部署管道中使用不同的命令行参数来进行代码构建；4. 部署成功后的服务加载不同的Nacos配置信息。此外，我们还增加了自动化单元测试和集成测试，确保代码质量和稳定性。

    系统在2023年10月顺利上线，系统上线后取得了显著成效：部署时间从2小时缩短至8分钟，硬件投入减少50%，服务响应时间提升60%。截至到目前为止，该系统已先后推广到海内外8家工厂。云原生架构在MES系统中的实践证明，通过合理运用服务化、弹性、可观测性和自动化等设计原则，能够有效提升系统的可扩展性、可靠性和运维效率。
    
    后续我们也在持续不断地对该系统进行优化更新，例如：使用文生sql技术对数据分析功能模块进行了更新，允许用户通过问答的方式自动生成sql查询语句并查询数据，但该功能目前在对索引的使用，大小表的join顺序还有待进一步优化；利用深度学习+图形分析技术，对不良品的瑕疵点进行标注，减少质量检验人员的操作时间，提高生产效率。计划进一步探索AI技术在云原生架构中的应用，研究自然语言生成服务代码和数据库表的可能性，持续优化系统架构，提升开发效率和系统性能。

