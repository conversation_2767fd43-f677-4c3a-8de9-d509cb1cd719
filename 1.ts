import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
// import Components from 'unplugin-vue-components/vite'
// import AutoImport from 'unplugin-auto-import/vite'
// import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Vue from '@vitejs/plugin-vue'
import WindiCSS from 'vite-plugin-windicss'
import VueJsx from '@vitejs/plugin-vue-jsx'
import EslintPlugin from 'vite-plugin-eslint'
import VueI18n from '@intlify/vite-plugin-vue-i18n'
// import styleImport, { ElementPlusResolve } from 'vite-plugin-style-import'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import PurgeIcons from 'vite-plugin-purge-icons'
import { viteMockServe } from 'vite-plugin-mock'
import DefineOptions from 'unplugin-vue-define-options/vite'
import { createHtmlPlugin } from 'vite-plugin-html'
import OptimizationPersist from 'vite-plugin-optimize-persist'
import PkgConfig from 'vite-plugin-package-config'
import viteCompression from 'vite-plugin-compression'
import vueSetupExtend from 'unplugin-vue-setup-extend-plus/vite'
import legacy from '@vitejs/plugin-legacy'

// https://vitejs.dev/config/
const root = process.cwd()

function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv(process.argv[3] === '--mode' ? process.argv[4] : process.argv[3], root)
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: isBuild ? './' : env.VITE_BASE_PATH,
    plugins: [
      legacy({
        targets: ['firefox 58'], //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        renderLegacyChunks: true,
        polyfills: [
          'es.symbol',
          'es.array.filter',
          'es.array.map',
          'es.array.splice',
          'es.promise',
          'es.promise.all',
          'es.promise.resolve',
          'es.promise.reject',
          'es.promise.catch',
          'es.promise.finally',
          'es/map',
          'es/set',
          'es.array.for-each',
          'es.object.define-properties',
          'es.object.define-property',
          'es.object.get-own-property-descriptor',
          'es.object.get-own-property-descriptors',
          'es.object.keys',
          'es.object.to-string',
          'web.dom-collections.for-each',
          'web.queue-microtask',
          'esnext.global-this',
          'esnext.string.match-all'
        ]
      }),
      viteCompression(), //gzip
      PkgConfig(),
      OptimizationPersist(),
      Vue(),
      VueJsx(),
      WindiCSS(),
      // AutoImport({
      //   include: [
      //     /\.tsx?$/, // .ts, .tsx,
      //     /\.jsx$/, // .jsx
      //     /\.vue$/
      //   ],
      //   dirs: ['src/**'], //,'src/components/Mes'
      //   resolvers: [ElementPlusResolver()],
      //   dts: 'types/auto-imports.d.ts'
      // }),
      // Components({
      //   // 指定组件位置，默认是src/components
      //   dirs: ['src/components/b'], //,'src/components/Mes'
      //   dts: 'types/components.d.ts',
      //   resolvers: [ElementPlusResolver()]
      // }),
      // styleImport({
      //   resolves: [ElementPlusResolve()],
      //   libs: [
      //     {
      //       libraryName: 'element-plus',
      //       esModule: true,
      //       resolveStyle: (name) => {
      //         return `element-plus/es/components/${name.substring(3)}/style/css`
      //       }
      //     }
      //   ]
      // }),
      EslintPlugin({
        include: ['src/**/*.vue', 'src/**/*.ts', 'src/**/*.tsx'] // 检查的文件
      }),
      VueI18n({
        runtimeOnly: true,
        compositionOnly: true,
        include: [resolve(__dirname, 'src/locales/*.json')]
      }),
      createSvgIconsPlugin({
        iconDirs: [pathResolve('src/assets/svgs')],
        symbolId: 'icon-[dir]-[name]',
        svgoOptions: true
      }),
      PurgeIcons(),
      viteMockServe({
        ignore: /^\_/,
        mockPath: 'mock',
        localEnabled: env.VITE_MOCK_SERVER,
        prodEnabled: env.VITE_MOCK_SERVER,
        supportTs: true, // 打开后，可以读取 ts 文件模块。 请注意，打开后将无法监视.js 文件。
        watchFiles: true, // 监视文件更改
        injectCode: `
          import { setupProdMockServer } from '../mock/_createProductionServer'

          setupProdMockServer()
          `
      }),
      vueSetupExtend({
        /* options */
      }),
      DefineOptions(),
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE,
            injectScript: `<script src="./inject.js"></script>`
          }
        }
      })
    ],

    css: {
      preprocessorOptions: {
        less: {
          additionalData: '@import "./src/styles/variables.module.less";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.less', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        },
        {
          find: /\_v\//,
          replacement: pathResolve('src/views') + '/'
        },
        {
          find: /\_c\//,
          replacement: pathResolve('src/components') + '/'
        }
      ]
    },
    build: {
      minify: 'terser',
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
      brotliSize: false,
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
          drop_console: env.VITE_DROP_CONSOLE === 'true'
        }
      }
    },
    server: {
      port: 3000,
      proxy: {
        // 选项写法
        // '/api': {
        //   target: 'http://localhost:3000',
        //   changeOrigin: true,
        //   rewrite: path => path.replace(/^\/api/, '')
        // }
        // 这里是通过请求/api 来转发到 https://api.pingping6.com/
        // // 假如你要请求https://api.*.com/a/a
        // // 那么axios的url，可以配置为 /api/a/a
        //     '/CamstarPortal': 'http://*************/'
        //   }
      },
      hmr: {
        overlay: false
      },
      host: '0.0.0.0'
    },
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vue-types',
        'element-plus/es/locale/lang/zh-cn',
        'element-plus/es/locale/lang/en',
        '@iconify/iconify',
        '@vueuse/core',
        'axios',
        'qs',
        'echarts',
        'echarts-wordcloud',
        'qrcode',
        '@wangeditor/editor',
        '@wangeditor/editor-for-vue',
        'element-plus/es/components/button-group/style/css',
        'element-plus/es/components/checkbox-button/style/css',
        'element-plus/es/components/color-picker/style/css',
        'element-plus/es/components/rate/style/css',
        'element-plus/es/components/slider/style/css',
        'element-plus/es/components/transfer/style/css',
        'element-plus/es/components/tree-select/style/css',
        'element-plus/es/components/badge/style/css',
        'intro.js/introjs.css'
      ]
    }
  }
}
