<!--
 * @Author: WDD
 * @Date: 2025-05-17 15:49:32
 * @LastEditors: WDD
 * @LastEditTime: 2025-05-20 10:24:13
 * @Description: file content
-->
## 简介

2023年4月，为提高企业综合竞争力，我司正式启动了以制造执行系统MES为基石的数字化建设项目。作为系统架构师，我主导了本项目的整体架构设计和实施工作。本文以该项目为蓝本，深入探讨了容错技术在项目中的应用。通过云原生架构结合Kubernetes实现服务的水平冗余；通过服务器和Redis集群技术实现数据存储的容错；通过软件容错技术实现应用程序的容错，保障系统的高可用。该项目于2023年10月正式上线，并逐步覆盖海内外8家生产基地，成效斐然。

## 正文

在制造业数字化的浪潮下，某省2022年工业互联网报告指出：73%的制造业因传统MES架构僵化，跨厂区协同效率低于业内平均水平。这些MES系统多采用单体架构，灵活性差，业务扩展困难，硬件成本达42%，难以实现柔性化生产，形成信息孤岛，系统整合困难，阻碍企业数字化转型。

为应对生产数据实时采集能力不足、跨厂区协同效率低下的问题，我司于2023年4月正式启动了新一代云原生MES系统建设，旨在通过架构革新实现制造生产全流程的敏捷响应。该项目采用三阶段迭代开发模式，首次投入800万，覆盖1个生产基地，5个厂区，接入1000+台工业设备。MES系统是生产制造的重要工具，它具有生产计划动态调度、生产数据实时采集、工艺流程控制、质量控制、物料追溯和设备状态管理等功能，打通了企业计划层和制造现场的信息壁垒，实现生产的透明化和柔性化。作为系统架构师，我主导本项目的整体架构设计和实施，基于业务边界将系统细分为10大微服务，保障系统的99.9999%可用性。截至到2025年3月，该系统已成功上线了10个微服务，单生产基地日产量达200MW，跨厂区协同效率提升68%。

可靠性是系统发生故障后能够快速完成故障的定位和处理，确保系统能继续正常运行的能力。保障可靠性的技术包括避错技术、检错技术、容错技术和降低复杂度设计等，其中避错技术是指在系统正式运行之前进行测试和评审来发现和改正错误从而规避风险；检错技术则是通过在易出错的地方设置监测点，通过判断结果返回是否异常、是否超时、标志位是否正确等来确定服务是否发生错误，但这种技术无法自动对错误进行修复；容错技术则是通过多机部署、负载均衡、自动修复等手段来实现；降低复杂度设计则是通过优化算法、简化软件结构、优化数据流向等手段来提高软件的可读性和可维护性，降低程序出错的可能性。容错技术中包含冗余技术、软件容错技术、双机容错技术和集群技术，其中冗余技术又包括结构冗余、信息冗余和时间冗余；软件容错技术则又包括N版本设计、恢复块设计和防卫式程序设计；双机容错技术则包括双机热备、双机互备和双机双工。

在该项目中，我采用容错技术对云原生架构进行设计，并从冗余设计、软件容错设计、集群设计三个详细介绍具体实施过程和效果。

1.	性能场景评估
传统MES系统中采用单体架构，模块间相互耦合，难以水平扩展，向上扩展时硬件成本高昂，服务器硬件性能成了系统性能的瓶颈，随着业务的增长，核心服务的响应时间平均达500ms，部分功能的响应时间更是长达几分钟。

2.	安全性场景评估
3.	可修改性场景评估

2023年10月，该系统已成功上线，并逐步覆盖海内外8个生产基地，取得了令人瞩目的成效：部署时间从之前的2小时缩短到8分钟、核心服务TP99<300ms、服务可用性达99.9999%、服务响应时间提升60%、硬件成本降低40%。ATAM架构评估方法在该项目中的实施证明，合理科学的运用评估活动可以有效提高系统性能、安全性、可用性和维护性。

展望未来，我们持续不断地对系统进行优化升级，例如使用文生SQL技术革新数据分析模块，用户可通过自然语言提问的方式自动生成查询语句和数据报表，大大提高了查询速度和报表开发效率。但该功能在索引利用和大小表连接方面仍有优化空间。同时，我们还通过深度学习结合图像分析技术，对不良品瑕疵点进行精准标注，提升了质检人员的工作效率，减轻了工作负担。后续，我们将继续发掘AI技术在云原生MES系统中的应用潜力，研究结合低代码开发平台通过自然语言生成前端界面、业务代码和数据库表的可能性，以期待持续优化系统架构，提升开发效率于系统性能，为企业数字化转型提供助力。
