<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魏冬冬 - 简历</title>
    <style>
        body {
            font-family: "Segoe UI", "微软雅黑", sans-serif;
            line-height: 1.6;
            max-width: 960px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 25px;
        }

        h2 {
            color: #3498db;
            margin: 25px 0 15px;
        }

        h3 {
            color: #2c3e50;
            margin: 20px 0 10px;
        }

        .section {
            background: white;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .contact-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
            border-left: 2px solid #3498db;
            margin-left: 15px;
        }

        .timeline-item {
            margin-bottom: 30px;
            position: relative;
        }

        .timeline-item::before {
            content: "";
            position: absolute;
            left: -35px;
            top: 5px;
            width: 12px;
            height: 12px;
            background: #3498db;
            border-radius: 50%;
        }

        .project-details {
            margin-left: 20px;
            border-left: 2px solid #eee;
            padding-left: 20px;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 500;
        }

        ul {
            padding-left: 25px;
        }

        li {
            margin-bottom: 8px;
        }

        a {
            color: #3498db;
            text-decoration: none;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            background: #3498db;
            color: white;
            border-radius: 4px;
            font-size: 0.9em;
            margin: 2px;
        }
    /* 打印样式优化 */
    @media print {
        /* 基本打印设置 */
        body {
            width: 100%;
            margin: 0;
            padding: 0;
            background-color: white;
            print-color-adjust: exact; /* 尝试保留颜色 */
            -webkit-print-color-adjust: exact;
        }

        /* 修复badge在打印时的显示问题 */
        .badge {
            display: inline;
            background-color: transparent !important;
            color: #333 !important;
            border: 1px solid #3498db;
            padding: 2px 6px;
            border-radius: 2px;
        }

        /* 修复timeline-item在打印时的显示问题 */
        .timeline {
            border-left: 1px solid #3498db;
            padding-left: 20px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        /* 替代timeline-item::before伪元素 */
        .timeline-item::before {
            content: "•";
            position: absolute;
            left: -25px;
            color: #3498db;
            font-size: 20px;
            line-height: 1;
        }

        /* 确保背景色和阴影在打印时不会丢失或导致内容不可见 */
        .section {
            background: transparent !important;
            box-shadow: none !important;
            border: 1px solid #ddd;
            margin-bottom: 20px;

        }

        /* 确保文本颜色在打印时足够深，保证可读性 */
        body, h1, h2, h3, p, li {
            color: black !important;
        }

        /* 确保链接在打印时可见 */
        a {
            color: #0066cc !important;
            text-decoration: underline;
        }


        /* 调整列布局在打印时的表现 */
        [style*="column-count"] {
            column-count: 2 !important; /* 确保列布局在打印时保持 */
        }

        /* 确保logo在打印时正确显示 */
        header img {
            display: block !important;
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
        }
    }
    </style>
</head>

<body>
    <header>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h1 style="margin: 0;">魏冬冬</h1>
            <div style="text-align: right;">
                <img src="images/longcheer_logo.png" alt="龙旗科技" style="height: 40px; margin-bottom: 5px;">
                <div style="font-size: 14px; color: #666;">上海龙旗科技股份有限公司</div>
            </div>
        </div>
        <div class="contact-info">
            <div>
                <span>📱 18024351176</span><br>
                <a href="mailto:<EMAIL>">📧 <EMAIL></a>
            </div>
            <div>
                <span class="badge">工业软件行业</span>
                <span class="badge">7年经验</span>
            </div>
        </div>
    </header>

    <!-- 教育背景 -->
    <section class="section">
        <h2>🎓 教育背景</h2>
        <div class="timeline">
            <div class="timeline-item">
                <h3>武汉理工大学 · 机械工程及自动化</h3>
                <p>硕士 | 2015.9 - 2018.6</p>
            </div>
            <div class="timeline-item">
                <h3>武汉理工大学 · 机械制造及其自动化</h3>
                <p>学士 | 2011.9 - 2015.6</p>
            </div>
        </div>
    </section>

    <!-- 工作经验 -->
    <section class="section">
        <h2>💼 工作经验</h2>
        <div class="timeline">
            <!-- 正泰新能 -->
            <div class="timeline-item">
                <h3>MES 高级开发 · 正泰新能科技股份有限公司</h3>
                <p>2023.11 - 至今</p>
                <ul>
                    <li>负责电池车间 MES 系统功能开发与并发优化，以 Camstar 为核心构建生产执行系统</li>
                    <li>主导 MES 与 EAP、设备、AGV 等周边系统的深度集成，实现全流程自动化生产</li>
                    <li>主导线边仓项目的架构设计与实施，建立物料智能管控体系，实现水位线实时监控与自动补货</li>
                    <li>设计 PDA 架构，支持快速开发框架与无感发布机制，实现多基地标准化部署</li>
                </ul>
            </div>

            <!-- 天圣华 -->
            <div class="timeline-item">
                <h3>MOM 开发顾问 · 北京天圣华信息技术有限责任公司</h3>
                <p>2021.05 - 2023.11</p>
                <ul>
                    <li>主要负责自研 MES 系统的架构设计、技术选型和底层代码开发</li>
                    <li>低代码引擎的主力开发之一，并以此为基础搭建 MES 系统建模和事务页面</li>
                    <li>负责自研 MES 系统的重难点技术突破和代码优化工作，提高了系统的运行效率和响应速度</li>
                </ul>
            </div>

            <!-- 华勤技术 -->
            <div class="timeline-item">
                <h3>MES 开发工程师 · 华勤技术股份有限公司</h3>
                <p>2018.04 - 2021.04</p>
                <ul>
                    <li>负责工厂端的 MES 系统开发运维工作，其中包括 4 个客户、4 种产品、4 个工厂和 6 个生产环境</li>
                    <li>负责 WMS 项目的一期二期开发工作，成功搭建系统架构并部署中间件</li>
                    <li>负责新 MES 系统的开发工作，包括逻辑迁移和界面设计开发，提高用户体验和系统性能</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- 重点项目 -->
    <section class="section">
        <h2>🚀 重点项目</h2>
        <div class="timeline">
            <!-- MES系统升级 -->
            <div class="timeline-item">
                <h3>MES 系统升级与集成</h3>
                <p><span class="highlight">项目目的：</span>
                <ul>
                    <li>原 MES 系统基于 IIS 部署，存在单点故障风险且扩展性不足；</li>
                    <li>F5 硬件负载均衡成
                        本高昂，难以应对日均百万级数据交互需求。</li>
                    <li>需通过架构升级解决并发性能瓶颈，
                        并实现与 AGV、EAP、PLC 等系统的深度集成，支撑智能化车间建设。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>容器化架构重构。采用 K8s 替代 IIS 部署方案，实现服务弹性伸缩与自动恢复，提升资
                        源利用率 40%；通过 K8s Service 实现服务发现机制，替代 F5 硬件负载均
                        衡，运维成本降低 60%；使用 Azure 设计 CI/CD 流水线，实现灰度发布与版本回
                        滚。 </li>
                    <li>高并发性能优化。构建多级缓存体系：前端使用 LocalStorage 缓存静态配置，后
                        端通过 Redis 集群缓存热点数据（工艺参数/设备状态），数据库查询压力下降
                        70%；优化线程池模型与连接池配置，关键接口 TP99 从 1.2s 降至 300ms。</li>
                    <li>自动化运维体系。基于 RabbitMQ 消息队列 + 进程调用框架，实现 500+ 台工控
                        机程序远程无感更新，运维效率提升 90%；开发心跳检测模块，通过 gRPC 长连
                        接实时监控工控机状态，异常恢复时间缩短至 30s。 </li>
                    <li>跨系统集成。制定 RESTful API 规范，完成与 AGV 调度系统（路径规划）、EAP
                        （设备指令下发）、PLC（实时数据采集）的交互；对接工业设备，封装通用通信
                        中间件，兼容 Modbus/TCP、Profinet 等协议。 </li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>掌握 K8s 集群管理、服务网格（Istio）、自动化发布和测试等云原生技术栈；</li>
                    <li>深化高并发场景下的架构设计能力（缓存分层/异步化/资源隔离）；
                    </li>
                    <li>掌握工控设备的对接方法，涵盖协议适配、安全通信、自动化运维等环节。 </li>
                </ul>
                </p>
            </div>

            <!-- 线边仓项目 -->
            <div class="timeline-item">
                <h3>线边仓物料管控项目</h3>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>公司生产车间因物料领用流程混乱、线边仓库存水位不透明，导致频繁出现物料积
                        压或短缺，平均每月产线停机时长超 40 小时，物料损耗成本占比达 12%。</li>
                    <li>需构建
                        与 MES/WMS 联动的全流程物料管控体系，实现工单级物料追溯、动态水位预警
                        与成本精细化分析，目标将齐套率提升至 98%、物料损耗成本降低 8%。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>工单物料全生命周期管控。开发 MES 工单驱动模块，基于.Net Core + Kafka 实现
                        工单创建→物料领用（与 WMS 接口交互）→工序过站扣减（RFID 采集）→尾料
                        退回的全链路闭环，工单执行效率提升 35%；设计物料防错机制：通过 PDA 扫码
                        校验 BOM 版本与实物一致性，错误投料率下降 90%。</li>
                    <li>智能水位线监控系统。构建动态水位模型：基于时间序列预测（LSTM）分析历史
                        消耗数据，按工单优先级动态调整安全库存阈值；开发实时预警引擎：通过
                        RabbitMQ + SignalR 推送缺料预警至看板与移动端，缺料响应时间从 2 小时缩短
                        至 10 分钟。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>系统集成深度：掌握 MES/WMS/ERP 三系统数据协同方案，精通跨平台接口设计
                        （如 WMS 库存冻结接口、ERP 成本分摊接口）；</li>
                    <li>实时计算能力：熟练运用流式计算框架（Kafka Streams）实现毫秒级水位预警，
                        TP99<500ms；</li>
                    <li>业务闭环思维：通过「监控→预警→分析→优化」闭环，推动产线物料管理从被动
                        响应转向主动预防。</li>
                </ul>
                </p>
            </div>

            <!-- 自研MES系统 -->
            <div class="timeline-item">
                <h3>自研 MES 系统</h3>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>当前 MES 系统不满足客户的离散型行业的性质，需要进行大量的二次开发；</li>
                    <li>部分页面加载过慢，需要进行性能优化；</li>
                    <li>部分扩展功能在多个客户处被重复开发，没有进行模块复用。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>自研 MES 系统的架构设计和技术选型。为了性能和平台适配，选用 VUE3.0 作为
                        前端语言，.net core 作为后端开发语言，具有原代码可复用、性能最优、平台可迁
                        移、发布便捷等优点；拆分业务模块，搭建分布式服务架构；使用 docker、
                        Jenkins、Nginx 等技术实现自动化部署和快速交付；</li>
                    <li>重难点技术突破。例如工艺管理模块、自定义表单设计器和低代码生成器等功能模
                        块设计和开发；pnpm 工作空间拆分，便于组件打包和二次开发；基于 EF Core 的
                        复杂模型 CRUD 通用 O/RM 框架开发；基于 VuePress 的项目开发文档编辑器。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>分布式服务架构搭建和部署；</li>
                    <li>Vue3.0、.net core 的开发能力，windi css、EF Core、VuePress、pnpm、
                        BPMN、MongoDB、Kubernetes 等技术的应用；</li>
                    <li>低代码设计和重难点技术突破思路的提升。</li>
                </ul>
                </p>
            </div>

            <!-- MES新系统 -->
            <div class="timeline-item">
                <h3>MES 新系统</h3>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>此前 MES 系统是单体架构，存在以下几个问题：</li>
                    <li>一是难以对中间件进行扩展，新技术难以落地；</li>
                    <li>二是系统压力主要集中在数据库，需要频繁调优；</li>
                    <li>三是代码复用性较差，耦合性过强；</li>
                    <li>因此，需要开发出一套新系统，解决以上问题。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>将现有系统种的逻辑迁移到新系统中，该系统采用 Springboot 框架，Java 开发；</li>
                    <li>设计界面，调优功能。该系统采用 VUE+Element 作为前端界面设计。</li>
                    <li>协同处理开发过程中出现的问题。例如 maven 包拉取失败，dubbo 服务的本地调
                        用。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>zookeeper、dubbo、Apollo、Gerrit 的安装和部署；</li>
                    <li>Idea(包括常用插件 Jrebel、mybatislog 等)的使用；</li>
                    <li>Springboot、VUE+Element 的开发，单元测试。</li>
                </ul>
                </p>
            </div>

            <!-- WMS项目 -->
            <div class="timeline-item">
                <h3>WMS 项目一期、二期</h3>
                <p><span class="highlight">项目目的：</span></p>
                <p>WMS 软件也叫仓库管理系统，是对公司采购的物料、客供料的采集，库存的管理，出
                    货的调度等。之前购买的 WMS 软件维护成本高，而且难以二次开发，需要根据自身的
                    业务自研一款 WMS 系统。</p>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>架构设计与搭建，工具链的部署。架构选用 DDD(基于领域驱动的设计模式)，需要
                        兼顾 PDA、winform、web、api 和微信小程序等共同使用；中间件主要涉及
                        Nginx(web、api 分别部署两台服务器)、Jenkins(自动发布程序到服务器)、ELK(收
                        集和分析 api 的 log)、Kafka(主要负责微信服务的推送和邮件的发送)、docker(挂
                        载 web 和 api 服务)。</li>
                    <li>独自负责入库模块、标签打印、程式自动更新等功能的开发；共同完成报表查询的
                        开发；独自负责与客户接口的交互。</li>
                    <li>二期的需求分析和开发工作，优化系统功能和性能。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>DDD 架构模式的设计与搭建，Nginx、Jenkins、ELK、Kafka、docker 等工具链
                        的部署与应用；</li>
                    <li>业务面拓展，基本了解 WMS 系统的业务流程以及与 MES、ODS 等系统的交互；</li>
                    <li>PDA(手持终端)、.net core、微信小程序等的开发。</li>
                </ul>
                </p>
            </div>

            <!-- MES系统开发和运维 -->
            <div class="timeline-item">
                <h3>MES 系统开发和运维工作</h3>
                <p><span class="highlight">项目目的：</span></p>
                <p>MES 系统主要负责对工厂端的物料追溯、质量管控、工艺管理、数据汇总等。</p>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>作为运维工程师。确保生产环境正常运作，随时解答产线问题，处理系统异常，处理
                        ESB 总线(与 PLM、ERP、HR 等系统的交互)的报错；熟练掌握发布程序、部署
                        网站和 API 的技能；</li>
                    <li>作为 BA。对用户提出的需求进行分析，转化为业务逻辑，如有必要还需输出流程
                        图；</li>
                    <li>作为开发工程师。根据分许出的业务逻辑，对现有的 winform、wpf、web、api、
                        sql server 存储过程等进行开发，在满足需求的前提下，还需要考虑逻辑最优化；
                        与供应商的设备进行对接；回传客户数据等；针对系统不完善之处进行设计和方案
                        制定，并最终实施落地，如工艺流程图形化、数据库同步等。</li>
                    <li>作为 DBA。此前我们的 MES 系统还是单体架构，owner 除了运维、开发之外，还
                        需具备能根据数据库性能分析软件，对负责的数据合理迁移，sql 语句调优，分库
                        分表等能力。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>业务分析，程序、网站、api 的发布和部署，设备对接，客户数据回传，数据库调
                        优；</li>
                    <li>.net Framwork、MVC、WPF、sql server 存储过程等的开发；</li>
                    <li>Redis、ELK、bootstrap、mxgraph(一款做流程图的 js 插件)、vue.js 等的应用。</li>
                </ul>
                </p>
            </div>
        </div>
    </section>

    <!-- 资格证书 -->
    <section class="section">
        <h2>🏆 资格证书</h2>
        <ul>
            <li>大学英语六级（CET-6）</li>
            <li>数据库系统工程师</li>
        </ul>
    </section>

    <section class="section">
        <h2>🌟 核心能力</h2>
        <div style="column-count: 2;">
            <ul style="margin-top: 0;">
                <li>工业软件平台：Camstar/Opcenter（专家级）</li>
                <li>云原生架构：K8s/Docker/Azure（生产级部署）</li>
                <li>高并发优化：Redis集群/异步队列</li>
            </ul>
            <ul>
                <li>工业协议：OPC UA/Modbus（项目实战）</li>
                <li>全栈开发：Vue3 + .NET Core + Ionic/Uniapp(多终端)</li>
                <li>数据分析：SPC、帆软（工业场景）</li>
            </ul>
        </div>
    </section>

</body>

</html>