<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魏冬冬 - 简历</title>
    <style>
        body {
            font-family: "Segoe UI", "微软雅黑", sans-serif;
            line-height: 1.6;
            max-width: 960px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 25px;
        }

        h2 {
            color: #3498db;
            margin: 25px 0 15px;
        }

        h3 {
            color: #2c3e50;
            margin: 20px 0 10px;
        }

        .section {
            background: white;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .contact-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
            border-left: 2px solid #3498db;
            margin-left: 15px;
        }

        .timeline-item {
            margin-bottom: 30px;
            position: relative;
        }

        .timeline-item::before {
            content: "";
            position: absolute;
            left: -35px;
            top: 5px;
            width: 12px;
            height: 12px;
            background: #3498db;
            border-radius: 50%;
        }

        .project-details {
            margin-left: 20px;
            border-left: 2px solid #eee;
            padding-left: 20px;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 500;
        }

        ul {
            padding-left: 25px;
        }

        li {
            margin-bottom: 8px;
        }

        a {
            color: #3498db;
            text-decoration: none;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            background: #3498db;
            color: white;
            border-radius: 4px;
            font-size: 0.9em;
            margin: 2px;
        }
    /* 打印样式优化 */
    @media print {
        /* 基本打印设置 */
        body {
            width: 100%;
            margin: 0;
            padding: 0;
            background-color: white;
            print-color-adjust: exact; /* 尝试保留颜色 */
            -webkit-print-color-adjust: exact;
        }

        /* 修复badge在打印时的显示问题 */
        .badge {
            display: inline;
            background-color: transparent !important;
            color: #333 !important;
            border: 1px solid #3498db;
            padding: 2px 6px;
            border-radius: 2px;
        }

        /* 修复timeline-item在打印时的显示问题 */
        .timeline {
            border-left: 1px solid #3498db;
            padding-left: 20px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        /* 替代timeline-item::before伪元素 */
        .timeline-item::before {
            content: "•";
            position: absolute;
            left: -25px;
            color: #3498db;
            font-size: 20px;
            line-height: 1;
        }

        /* 确保背景色和阴影在打印时不会丢失或导致内容不可见 */
        .section {
            background: transparent !important;
            box-shadow: none !important;
            border: 1px solid #ddd;
            margin-bottom: 20px;

        }

        /* 确保文本颜色在打印时足够深，保证可读性 */
        body, h1, h2, h3, p, li {
            color: black !important;
        }

        /* 确保链接在打印时可见 */
        a {
            color: #0066cc !important;
            text-decoration: underline;
        }


        /* 调整列布局在打印时的表现 */
        [style*="column-count"] {
            column-count: 2 !important; /* 确保列布局在打印时保持 */
        }

        /* 确保logo在打印时正确显示 */
        header img {
            display: block !important;
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
        }
    }
    </style>
</head>

<body>
    <header>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h1 style="margin: 0;">魏冬冬</h1>
            <div style="text-align: right;">
                <img src="images/longcheer_logo.png" alt="龙旗科技" style="height: 40px; margin-bottom: 5px;">
                <div style="font-size: 14px; color: #666;">上海龙旗科技股份有限公司</div>
            </div>
        </div>
        <div class="contact-info">
            <div>
                <span>📱 18024351176</span><br>
                <a href="mailto:<EMAIL>">📧 <EMAIL></a>
            </div>
            <div>
                <span class="badge">工业软件行业</span>
                <span class="badge">7年经验</span>
            </div>
        </div>
    </header>

    <!-- 教育背景 -->
    <section class="section">
        <h2>🎓 教育背景</h2>
        <div class="timeline">
            <div class="timeline-item">
                <h3>武汉理工大学 · 机械工程及自动化</h3>
                <p>硕士 | 2015.9 - 2018.6</p>
            </div>
            <div class="timeline-item">
                <h3>武汉理工大学 · 机械制造及其自动化</h3>
                <p>学士 | 2011.9 - 2015.6</p>
            </div>
        </div>
    </section>

    <!-- 工作经验 -->
    <section class="section">
        <h2>💼 工作经验</h2>
        <div class="timeline">
            <!-- 正泰新能 -->
            <div class="timeline-item">
                <h3>MES 高级开发 · 正泰新能科技股份有限公司</h3>
                <p>2023.11 - 至今</p>
                <ul>
                    <li>负责电池车间 MES 系统功能开发与并发优化，以 Camstar 为核心构建生产执行系统</li>
                    <li>主导 MES 与 EAP、设备、AGV 等周边系统的深度集成，实现全流程自动化生产</li>
                    <li>主导线边仓项目的架构设计与实施，建立物料智能管控体系，实现水位线实时监控与自动补货</li>
                    <li>设计 PDA 架构，支持快速开发框架与无感发布机制，实现多基地标准化部署</li>
                </ul>
            </div>

            <!-- 天圣华 -->
            <div class="timeline-item">
                <h3>MOM 开发顾问 · 北京天圣华信息技术有限责任公司</h3>
                <p>2021.05 - 2023.11</p>
                <ul>
                    <li>主要负责自研 MES 系统的架构设计、技术选型和底层代码开发</li>
                    <li>低代码引擎的主力开发之一，并以此为基础搭建 MES 系统建模和事务页面</li>
                    <li>负责自研 MES 系统的重难点技术突破和代码优化工作，提高了系统的运行效率和响应速度</li>
                </ul>
            </div>

            <!-- 华勤技术 -->
            <div class="timeline-item">
                <h3>MES 开发工程师 · 华勤技术股份有限公司</h3>
                <p>2018.04 - 2021.04</p>
                <ul>
                    <li>负责工厂端的 MES 系统开发运维工作，其中包括 4 个客户、4 种产品、4 个工厂和 6 个生产环境</li>
                    <li>负责 WMS 项目的一期二期开发工作，成功搭建系统架构并部署中间件</li>
                    <li>负责新 MES 系统的开发工作，包括逻辑迁移和界面设计开发，提高用户体验和系统性能</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- 重点项目 -->
    <section class="section">
        <h2>🚀 重点项目</h2>
        <div class="timeline">
            <!-- MES系统升级 -->
            <div class="timeline-item">
                <h3>MES 系统升级与集成</h3>
                <p>2024.03 - 2024.10</p>
                <p><span class="highlight">项目目的：</span>
                <ul>
                    <li>原 MES 系统基于 IIS 部署，存在单点故障风险且扩展性不足；</li>
                    <li>F5 硬件负载均衡成
                        本高昂，难以应对日均百万级数据交互需求。</li>
                    <li>需通过架构升级解决并发性能瓶颈，
                        并实现与 AGV、EAP、PLC 等系统的深度集成，支撑智能化车间建设。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>云原生架构重构。基于 Camstar 核心平台进行微服务化改造，采用 Kubernetes
                        替代传统 IIS 部署架构，实现服务弹性伸缩与故障自愈，资源利用率提升 40%；
                        通过 K8s Service Mesh 构建服务发现与负载均衡机制，替代 F5 硬件设备，
                        运维成本降低 60%；集成 Nacos 实现分布式配置中心，支持配置热更新；
                        构建基于 Azure DevOps 的 CI/CD 流水线，实现代码提交→自动化测试→灰度发布→
                        版本回滚的全流程自动化，发布效率提升 80%。</li>
                    <li>高并发性能优化。构建多级缓存体系：前端使用 LocalStorage 缓存静态配置，后
                        端通过 Redis 集群缓存热点数据（工艺参数/设备状态），大幅降低数据库查询压力；
                        优化线程池模型与连接池配置，关键接口 TP99 从 1.2s 降至 300ms。</li>
                    <li>自动化运维体系。基于 RabbitMQ 消息队列 + 进程调用框架，实现 500+ 台工控
                        机程序远程无感更新，运维效率提升 90%；开发心跳检测模块，通过 gRPC 长连
                        接实时监控工控机状态，异常恢复时间缩短至 30s。 </li>
                    <li>质量控制体系建设。构建基于 SPC（统计过程控制）的全面质量监控体系，
                        部署多类型控制图：均值-极差控制图（X̄-R图）、中位数-极差控制图（M-R图）、
                        不合格品率控制图（P图）等，实现生产过程实时质量监控；集成判异规则引擎，
                        当检测到异常模式时自动触发邮件预警机制，质量异常响应时间缩短至5分钟内。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>掌握 K8s 集群管理、服务网格（Istio）、自动化发布和测试等云原生技术栈；</li>
                    <li>深化高并发场景下的架构设计能力（缓存分层/异步化/资源隔离）；</li>
                    <li>MES 系统业务流程及与工控设备对接 </li>
                </ul>
                </p>
            </div>

            <!-- 线边仓项目 -->
            <div class="timeline-item">
                <h3>线边仓物料管控项目</h3>
                <p>2024.05 - 2024.09</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>公司生产车间因物料领用流程混乱、线边仓库存水位不透明，导致频繁出现物料积
                        压或短缺，平均每月产线停机时长超 40 小时，物料损耗成本占比达 12%。</li>
                    <li>需构建
                        与 MES/WMS 联动的全流程物料管控体系，实现工单级物料追溯、动态水位预警
                        与成本精细化分析，目标将齐套率提升至 98%、物料损耗成本降低 8%。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>工单物料全生命周期管控。开发 MES 工单驱动模块，基于.Net Core + Kafka 实现
                        工单创建→物料领用（与 WMS 接口交互）→工序过站扣减（RFID 采集）→尾料
                        退回的全链路闭环，工单执行效率提升 35%；设计物料防错机制：通过 PDA 扫码
                        校验 BOM 版本与实物一致性，错误投料率下降 90%。</li>
                    <li>智能水位线监控系统。构建动态水位模型：基于时间序列预测（LSTM）分析历史
                        消耗数据，按工单优先级动态调整安全库存阈值；开发实时预警引擎：通过
                        RabbitMQ + SignalR 推送缺料预警至看板与移动端，缺料响应时间从 2 小时缩短
                        至 10 分钟。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>系统集成深度：掌握 MES/WMS/ERP 三系统数据协同方案，精通跨平台接口设计
                        （如 WMS 库存冻结接口、ERP 成本分摊接口）；</li>
                    <li>实时计算能力：熟练运用流式计算框架（Kafka Streams）实现毫秒级水位预警，
                        TP99<500ms；</li>
                    <li>业务闭环思维：通过「监控→预警→分析→优化」闭环，推动产线物料管理从被动
                        响应转向主动预防。</li>
                </ul>
                </p>
            </div>

            <!-- PDA移动端开发平台 -->
            <div class="timeline-item">
                <h3>PDA移动端开发平台</h3>
                <p>2024.06 - 2024.11</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>传统PDA应用开发周期长、部署复杂，难以快速响应各生产基地的个性化需求；</li>
                    <li>不同工厂、车间的业务流程差异较大，需要支持多租户架构以满足并发使用需求；</li>
                    <li>需要构建统一的移动端开发平台，实现快速开发、无感部署和硬件功能调用。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>跨平台框架搭建。基于 Ionic + Vue 构建 PDA 开发框架，支持 Android/iOS
                        双平台部署；集成 Cordova 插件体系，实现扫码、打印、RFID读取等硬件功能调用；
                        采用响应式设计，适配不同尺寸的手持设备，界面适配率达到 98%。</li>
                    <li>低代码开发平台。设计可视化表单设计器，支持拖拽式组件配置和业务流程编排；
                        构建模板引擎，预置常用业务场景（入库、出库、盘点、质检）模板，开发效率提升 70%；
                        实现热更新机制，通过菜单配置实现应用功能动态更新，无需重新安装。</li>
                    <li>多租户架构设计。基于租户隔离的数据架构，支持不同生产基地、工厂、车间的独立配置；
                        实现权限分级管理，支持企业级、工厂级、车间级的用户权限控制；构建负载均衡机制，
                        支持 1000+ 设备并发访问，响应时间稳定在 200ms 内。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>移动端开发技术栈：掌握 Ionic、Vue、Cordova 等跨平台开发技术，
                        熟练运用混合应用架构设计模式；</li>
                    <li>低代码平台设计：具备可视化开发工具设计能力，掌握模板引擎、热更新等核心技术；</li>
                    <li>多租户架构：深入理解 SaaS 架构设计原理，具备大规模并发系统的设计和优化能力；</li>
                    <li>硬件集成能力：熟练掌握移动设备硬件功能调用，具备工业级移动应用开发经验。</li>
                </ul>
                </p>
            </div>

            <!-- 自研MES系统 -->
            <div class="timeline-item">
                <h3>新一代MES制造执行系统</h3>
                <p>2021.05 - 2023.11</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>现有MES系统基于传统单体架构，难以适配离散制造业的复杂工艺流程，客户定制化开发成本高达60%；</li>
                    <li>系统性能瓶颈明显，关键页面加载时间超过1分钟，影响生产效率；</li>
                    <li>功能模块重复开发严重，代码复用率不足30%，维护成本持续攀升。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>微服务架构设计与实施。采用领域驱动设计（DDD）理念，基于Vue3 + .NET Core 6构建
                        前后端分离架构；按业务域拆分为工艺管理、质量控制、设备集成等10个核心微服务；
                        集成Docker容器化部署，通过Jenkins实现CI/CD流水线，部署效率提升75%；
                        使用Nginx实现API网关和负载均衡，系统并发处理能力提升3倍。</li>
                    <li>核心技术创新突破。设计可视化工艺路径编辑器，支持拖拽式工艺流程配置，工艺建模效率提升80%；
                        构建低代码表单设计器，基于JSON Schema动态渲染，支持48种组件类型；
                        开发通用ORM框架，基于EF Core实现多租户数据隔离和动态查询优化；
                        采用pnpm Workspaces架构，实现组件库统一管理，代码复用率提升至85%。</li>
                    <li>性能优化与监控体系。实施前端懒加载和虚拟滚动技术，页面首屏加载时间降至800ms；
                        集成Redis分布式缓存，热点数据查询性能提升90%；构建基于Grafana的
                        监控告警体系，实现系统健康度实时监控和故障预警。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>微服务架构设计：掌握DDD领域建模、服务拆分策略、分布式事务处理等核心技能，
                        具备大型系统架构设计和演进能力；</li>
                    <li>全栈技术深化：精通Vue3 Composition API、.NET Core高级特性、EF Core性能调优，
                        熟练运用Docker、MongoDB、BPMN等技术栈；</li>
                    <li>低代码平台建设：具备可视化开发工具设计思维，掌握元数据驱动、动态表单生成等
                        核心技术，推动开发模式创新。</li>
                </ul>
                </p>
            </div>

            <!-- MES新系统 -->
            <div class="timeline-item">
                <h3>MES分布式架构重构项目</h3>
                <p>2020.10 - 2021.04</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>原有MES系统采用单体架构，技术栈老化严重，中间件扩展能力不足，新技术集成困难；</li>
                    <li>系统性能瓶颈集中在数据库层，高并发场景下响应时间超过5秒，需要频繁进行数据库调优；</li>
                    <li>代码耦合度高达80%，模块复用性差，维护成本逐年递增；</li>
                    <li>亟需构建新一代分布式MES系统，提升系统可扩展性和性能表现。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>分布式架构设计与实施。基于Spring Boot构建分布式架构，
                        将单体应用拆分为多个服务；集成Dubbo实现
                        高性能RPC通信；采用Zookeeper作为注册中心，
                        实现服务自动发现和负载均衡；采用Apollo配置中心实现配置统一管理和动态更新。</li>
                    <li>前端技术栈升级。采用Vue 2.x + Element UI构建响应式前端界面，实现组件化开发；
                        集成Webpack优化构建流程，首屏加载时间从8秒优化至2秒；设计统一的UI组件库，
                        界面开发效率提升50%。</li>
                    <li>团队协作。协同处理开发过程中出现的问题。例如 maven 包拉取失败，dubbo 服务的本地调
                        用。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>分布式系统架构：掌握微服务拆分策略、服务治理、分布式事务处理等核心技能，
                        熟练运用Spring Boot生态技术栈及其单元测试构建；</li>
                    <li>前端工程化能力：精通Vue.js组件化开发、Webpack构建优化、前端性能调优等技术，
                        具备现代化前端架构设计能力；</li>
                </ul>
                </p>
            </div>

            <!-- WMS项目 -->
            <div class="timeline-item">
                <h3>仓储管理系统（WMS）一期、二期</h3>
                <p>2018.10 - 2021.04</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>原有商用WMS系统年维护费用高达50万元，且源码封闭无法进行深度定制开发；</li>
                    <li>系统功能与公司实际业务流程匹配度仅60%，需要大量人工干预和线下操作；</li>
                    <li>缺乏与MES、ERP等核心系统的深度集成，数据孤岛问题严重，影响供应链协同效率。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>多端统一架构设计。基于DDD领域驱动设计，构建支持PDA、WinForm、Web、API、
                        微信小程序的多端统一架构；采用.NET Core + Vue.js技术栈，实现前后端分离；
                        部署Nginx双机负载均衡，实现系统高可用；集成ELK日志分析平台，
                        实现全链路日志追踪和性能监控。</li>
                    <li>核心业务模块开发。独立设计并开发入库管理模块，支持多种入库策略和自动分配库位；
                        实现智能标签打印系统，集成条码/RFID双重识别机制；开发程序自动更新框架，
                        支持增量更新和版本回滚；构建可视化报表系统，提供20+维度的库存分析报表。</li>
                    <li>系统集成与优化。设计标准化API接口，实现与客户ERP系统的无缝对接；
                        集成Kafka消息队列，支持微信服务推送和邮件通知；使用Docker容器化部署，
                        部署时间从4小时缩短至30分钟；采用Jenkins实现自动构建和持续部署流水线。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>企业级架构设计：掌握DDD架构模式、多端统一开发、服务治理等核心技能，
                        具备复杂业务系统的架构设计和演进能力；</li>
                    <li>全栈技术整合：熟练运用.NET Core、Vue.js、Docker、Kafka等技术栈，
                        具备从需求分析到系统上线的全流程开发能力；</li>
                    <li>业务系统集成：深入理解WMS业务流程，掌握与MES、ERP、ODS等系统的集成方案，
                        具备供应链信息化建设经验。</li>
                </ul>
                </p>
            </div>

            <!-- MES系统开发和运维 -->
            <div class="timeline-item">
                <h3>MES制造执行系统全栈开发与运维</h3>
                <p>2018.04 - 2021.04</p>
                <p><span class="highlight">项目目的：</span></p>
                <ul>
                    <li>负责4个客户、4种产品线、4个工厂、6个生产环境的MES系统全生命周期管理；</li>
                    <li>实现物料追溯、质量管控、工艺管理、生产数据汇总等核心制造执行功能；</li>
                    <li>确保生产系统7×24小时稳定运行，支撑日均10万+工单的生产执行。</li>
                </ul>
                <p>
                    <span class="highlight">主要工作：</span>
                <ul>
                    <li>系统运维与保障。建立7×24小时运维监控体系，确保生产环境稳定运行；
                        快速响应产线问题，平均故障恢复时间控制在15分钟内；维护ESB企业服务总线，
                        处理与PLM、ERP、HR等系统的数据交互异常；掌握程序发布、网站部署、API服务管理等
                        运维技能，实现零停机发布。</li>
                    <li>业务分析与需求实现。担任业务分析师角色，深入生产现场调研用户需求，输出业务流程图和
                        系统设计方案；将复杂的制造业务逻辑转化为系统功能，累计完成50+需求分析和方案设计；
                        设计工艺流程可视化模块，使用mxGraph实现拖拽式工艺路径配置。</li>
                    <li>全栈开发与集成。基于.NET Framework + WPF/WinForm构建桌面应用，开发Web管理后台；
                        编写SQL Server存储过程，优化复杂查询性能，关键报表查询时间从30秒优化至3秒；
                        对接工业设备，实现上位机数据采集和设备状态监控；开发客户数据回传接口，
                        支持实时生产数据推送。</li>
                    <li>数据库管理与优化。担任DBA角色，负责SQL Server数据库的性能监控和调优；
                        实施数据库分库分表策略，解决单表数据量过大问题；进行SQL语句优化，
                        数据库整体性能提升40%；建立数据备份和恢复机制，确保数据安全。</li>
                </ul>
                </p>
                <p>
                    <span class="highlight">能力提升：</span>
                <ul>
                    <li>全栈开发能力：掌握.NET Framework、WPF、MVC、SQL Server等技术栈，
                        具备从前端到后端的完整开发能力；</li>
                    <li>运维保障体系：熟练掌握系统部署、监控、故障排查等运维技能，
                        具备生产环境管理和应急响应能力；</li>
                    <li>业务理解深度：深入理解制造业务流程，掌握MES系统核心功能，
                        具备业务分析和系统设计能力；</li>
                    <li>数据库专业技能：精通SQL Server管理、性能调优、数据架构设计，
                        具备大数据量场景下的数据库优化经验。</li>
                </ul>
                </p>
            </div>
        </div>
    </section>

    <!-- 资格证书 -->
    <section class="section">
        <h2>🏆 资格证书</h2>
        <ul>
            <li>大学英语六级（CET-6）</li>
            <li>数据库系统工程师</li>
        </ul>
    </section>

    <section class="section">
        <h2>🌟 核心能力</h2>
        <div style="column-count: 2;">
            <ul style="margin-top: 0;">
                <li>工业软件平台：Camstar/Opcenter（专家级）</li>
                <li>云原生架构：K8s/Docker/Azure（生产级部署）</li>
                <li>高并发优化：Redis集群/异步队列</li>
            </ul>
            <ul>
                <li>工业协议：OPC UA/Modbus（项目实战）</li>
                <li>全栈开发：Vue3 + .NET Core + Ionic/Uniapp(多终端)</li>
                <li>数据分析：SPC、帆软（工业场景）</li>
            </ul>
        </div>
    </section>

</body>

</html>