
一、结合自己所参与的软件项目，概要介绍该项目的背景及主要内容，并明确指出在其中所承担的主要任务和开展的主要工作。
二、常见的架构风格5大类，至少选2-3个类进行说明。（注意本部分内容虽然题目要求是详细论述，但实际上不是文章的重心，问题3才是结合项目的详细论述部分）
Garlan和Shaw将软件架构风格分为五大类，数据流风格、调用/返回风格、独立构件风格、虚拟机风格和仓库风格。其中：
（1）数据流风格包括批处理序列架构风格和管道/过滤器架构风格；
（2）调用/返回风格包括主程序/子程序架构风格、数据抽象和面向对象架构风格和层次结构架构风格；
（3）独立构件风格包括进程通信架构风格和事件驱动的架构风格；
（4）虚拟机风格包括解释器架构风格和基于规则的系统；
（5）仓库风格包括数据库架构风格和黑板架构风格。
其他的还有特定领域软件架构、状态转移等以及分布式处理等。其中分布式架构风格中有客户机/服务器风格、浏览器/服务器风格、CORBA、DCOM、EJB。
每一种具体的软件结构风格的模型如下：
1．数据流风格包括批处理序列和管道/过滤器架构风格。
（1）批处理序列架构风格。组件为一系列固定顺序的计算单元，组件间只通过数据传递交互。每个处理步骤是一个独立的程序，每一步必须在前一步结束后才能开始，数据必须是完整的，以整体的方式传递。
（2）管道/过滤器架构风格。每个构件都有一组输入和输出，构件读输入的数据流，经过内部处理，然后产生输出数据流。这个过程通常通过对输入流的变换及增量计算来完成，包括通过计算和增加信息丰富数据，通过浓缩和删除精炼数据，通过改变记录方式转化数据，递增地转化数据等。在输入被完全消费之前，输出便产生了。这里构件被称为过滤器，连接件就是数据流传输的管道，将一个过滤器的输出传到另一过滤器的输入。
2．调用/返回风格包括主程序/子程序架构风格、数据抽象和面向对象架构风格以及层次结构架构风格。
（1）主程序/子程序架构风格。单线程控制，把问题划分为若干处理步骤，构件即为主程序和子程序。子程序通常可合成为模块。过程调用作为交互机制，即充当连接件。调用关系具有层次性，其语义逻辑表现为主程序的正确性取决于它调用的子程序的正确性。
（2）数据抽象和面向对象架构风格。这种风格的构件是对象。对象是抽象数据类型的实例。在抽象数据类型中，数据的表示和它们的相应操作被封装起来。对象的行为体现在其接受和请求的动作。连接件即是对象间交互的方式，对象是通过函数和过程的调用来交互的。对象具有封装性，一个对象的改变不会影响其他对象。对象拥有状态和操作，也有责任维护状态。这种结构风格中包含有封装、交互、多态、集成、重用等特征。
（3）层次结构架构风格。层次系统组织成一个层次结构。构件在一些层实现了虚拟机。连接件通过决定层间如何交互的协议来定义，拓扑约束包括对相邻层间交互的约束。这个风格的特点是每层为上一层提供服务，使用下一层的服务，只能见到与自己邻接的层。大的问题分解为若干个渐进的小问题，逐步解决，隐藏了很多复杂度。修改一层，最多影响两层，而通常只能影响上层。上层必须知道下层的身份，不能调整层次之间的顺序。
3．独立构件风格包括进程通信架构风格和事件驱动的架构风格
（1）进程通信架构风格。构件是独立的过程，连接件是消息传递。这种风格的特点是构件通常是命名过程，消息传递的方式可以是点对点、异步和同步方式、以及远过程调用等
（2）事件驱动的架构风格。构件不直接调用一个过程，而是触发或广播一个或多个事件。系统中的其他构件中的过程在一个或多个事件中注册，当一个事件被触发，系统自动调用在这个事件中注册的所有过程。一个事件的触发就导致了另一个模块中的过程的调用。这种风格中的构件是非命名的过程，它们之间交互的连接件往往是以过程之间的隐式调用（Implicit Invocation）来实现的。基于事件的隐式调用风格的主要优点是为软件重用提供了强大的支持，为构件的维护和演化带来了方便，其缺点是构件放弃了对系统计算的控制。
4．虚拟机风格包括解释器架构风格和基于规则的系统
（1）解释器架构风格。一个解释器通常包括完成解释工作的解释引擎，一个包含将被解释的代码的存储区，一个记录解释引擎当前工作状态的数据结构，以及一个记录源代码被解释执行的进度的数据结构。具有解释器风格的软件中含有一个虚拟机，可以仿真硬件的执行过程和一些关键应用。其缺点是执行效率较低。
（2）基于规则的系统。基于规则的系统包括规则集、规则解释器、规则/数据选择器以及工作内存。
5．仓库风格包括数据库架构风格和黑板架构风格
（1）数据库架构风格。数据库架构是仓库风格最常见的形式。构件主要有两大类，一个是中央共享数据源，保存当前系统的数据状态，另一个是多个独立处理元素，处理元素对数据元素进行操作。
（2）黑板架构风格。黑板架构包括知识源、黑板、控制三部分。知识源包括若干独立计算的不同单元，提供解决问题的知识，知识源响应黑板上的变化，也只修改黑板。黑板是一个全局数据库，包含解域的全部状态，是知识源互相作用的唯一媒介。知识源响应是通过黑板状态的变化来控制。黑板通常应用在对于解决问题没有确定性算法的系统中，例如信号处理、问题规划、编译器优化等软件系统的设计中。
三、问题中明确要求回答选择架构的原因，其实是要求考生在组织论文时，说明作者选择架构的依据是什么，而各种架构应用在作者担任的项目中，有何优势与劣势。当这些内容分析清楚之后，合适的架构自然浮出水面来了。然后附带性的讲一讲架构的内容，架构具体内容与设计都已不是重心。最后谈一谈整体效果收尾。
</p>