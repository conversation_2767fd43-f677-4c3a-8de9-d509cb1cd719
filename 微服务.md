<!--
 * @Author: WDD
 * @Date: 2025-04-09 09:33:43
 * @LastEditors: WDD
 * @LastEditTime: 2025-04-14 09:35:17
 * @Description: file content
-->

## 简介
    本文以某制造企业MES系统项目为背景，探讨微服务架构在制造执行系统中的实践应用。2023年4月，响应某省制造业数字化转型的号召，我所在公司启动了以制造执行系统——MES为核心的信息化数智化建设。作为项目架构师，我主导了系统的架构设计工作。本文重点阐述了该系统采用微服务架构的技术选型与实现方案，并从系统性能、可靠性、可扩展性等维度，对比分析了微服务架构相较于传统单体架构的优势。系统整体划分为前端服务、平台保障服务和后端业务服务三个层次。前端服务采用Vue3、React等技术栈，实现了跨终端访问能力；平台保障服务以Kubernetes为核心，集成了Nacos、RabbitMQ等中间件，保障系统的高可用性；后端业务服务基于ASP.NET Core 6框架，按领域边界拆分为十大微服务模块。最终系统在2023年10月顺利上线，并逐步推广到海内外8家工厂，收到用户和领导的一致好评。

## 正文
    随着企业规模的不断扩大，工厂产量的不断提升，用户对为制造执行系统（以下简称为“MES系统”）的非功能需求越来越激烈，传统单体架构MES系统暴露出诸多问题：系统耦合度高、扩展性差、单点故障频发、运维成本高昂（年均硬件投入增长37%）、跨厂区协同效率低下。为解决这些问题，同时为响应某省制造业数字化转型的号召，提高企业核心竞争力，2023年4月，我所在公司启动了以制造执行系统——MES为核心的信息化数智化建设。
    基于领域驱动设计理念，我们将系统划分为十大业务域：生产计划与调度、工艺管理、生产管理、质量管理、设备管理、物料管理、数据分析、用户管理、日志追溯和系统集成。生产计划与调度模块主要用于处理SAP系统和PLM系统推送过来的订单、BOM数据，作为后续批次创建、批次入库等操作的信息依据，实现生产排程、任务调度和进度追踪等功能；工艺管理模块主要用于管理工艺流程，包括工艺路线定义、工艺参数定义、工艺文件管理和工艺版本管理等，确保生产过程的一致性和可重复性；生产管理模块主要用于采集生产数据，实时监控生产过程，确保生产按计划执行；质量管理模块主要实现质量检验计划制定、质量检测、质量分析和不良品管理等功能；设备管理模块主要有设备台账管理、设备状态监控、设备故障与维修记录、设备稼动率分析等功能；物料管理模块主要用于管理物料库存，包括物料需求计划、库存管理、工位上下料、物料追溯和库存预警等功能；数据分析模块主要用于对生产、质量、设备、物料等数据进行分析，提供决策支持；用户管理模块主要用于管理系统用户，包括用户权限管理、用户角色管理、排班管理等功能；日志追溯模块主要用于记录系统操作日志，实现产品批次追溯、物料追溯、设备追溯、质量追溯等功能；系统集成模块主要用于实现系统与外部系统的集成，包括与SAP、PLM、WMS、OA等系统的集成。
    在该项目中，我主要担任系统架构师，负责架构需求分析、架构设计、架构文档化和架构实现等工作。通过与生产业务代表、质量业务代表、设备供应商及上下游外部系统代表进行沟通，以及对系统的功能需求和质量属性需求进行分析，我们决定选用微服务架构来开发部署MES系统。微服务与之前的单块结构系统相比有着诸多优势，有：1. 弹性伸缩，微服务支持针对部分服务进行增加或减少资源，而单块结构系统则需要对整个系统进行伸缩，例如在业务繁忙时，需要对生产管理模块服务进行扩容以满足系统的响应性能和可靠性；2. 技术异构，微服务允许不同团队选择适合的技术栈，而单块结构系统则技术异构困难，在开发语言和数据库的选择上需要兼顾所有团队成员，例如在本系统中，每个模块的小组组长根据业务和成员的开发技能选择不同的技术栈，在满足功能需求前提下提高开发效率；3. 横向扩展，微服务更容易在水平方向上对服务进行扩展，而单块结构系统一般是向上扩展，即升级服务器硬件资源，这无疑增加了企业的设备成本；4. 独立部署，故障隔离，微服务架构在进行功能升级和版本迭代时风险更小，因为每一个功能模块都清楚定义了其服务边界，同时得益于Kubernetes的灰度发布，我们可以对部分功能进行上线和用户测试，极大的降低了风险，保证了系统稳定性和可靠性。
    MES系统的微服务架构从开发视图来看主要分为前端服务、平台保障服务和后端业务服务。下面针对这三类服务进行展开具体说明。
    1. 前端服务
    前端服务主要提供用户可访问、可操作的页面。在该项目中，前端界面需要支持在Web页面、Windows客户端、PDA（手持终端）、安卓和IOS设备等多终端上进行使用，所以我们选择了Vue3、React和ASP.NET Core等多种语言进行开发。Web页面主要负责工艺路线维护、质量标准维护、用户管理和日志追溯等功能，部署在Kubernetes中，实现负载均衡；客户端主要负责生产管理，用于人工操作工位的生产数据采集；PDA的主要功能是物料的领料和上下料、批次入库等操作；安卓和IOS的APP则主要用来查看数据分析后的报表和图表，让各部分负责人快速了解生产情况。Web页面、PDA、安卓和IOS APP我们采用虚拟机架构风格。用户打开前端界面进行登陆、查询和操作时，前端服务则会将Http请求发送给网关，网关则会现根据用户Token进行权限验证，然后调用后端用户管理微服务获取其对应权限，最后根据请求的路径和参数将请求转发到对应的后端业务服务，并将响应结果返回给前端。
    2. 平台保障服务
    平台保障服务主要保障系统安全、稳定、可扩展的运行。在该项目中，我们选择了Ocelot作为网关，Kubernetes作为容器编排系统，Nacos作为配置管理中心，RabbitMQ作为消息队列，ELK作为日志系统，Redis作为缓存系统，Azure作为git管理工具和CI/CD平台。Ocelot API网关可以实现请求的路由、限流、熔断、安全防护和日志记录等功能，用于对前端服务和外部系统提供统一的域名访问入口；Kubernetes可以实现容器的自动部署、自动伸缩、自动更新、自动回滚等功能，保证系统的高可用性，减少50%硬件投入；Nacos可以实现服务的配置管理，允许运行时注册和热更新配置，减少服务的重启，提高系统的可修改性；RabbitMQ可以实现消息的异步处理，提高系统的性能，同时也是实现分布式事务的重要中间件；ELK可以实现日志的收集、存储、查询和分析，实现毫秒级异常追踪；Redis可以实现缓存的自动管理，显著提高系统的查询性能和响应速度；基于Azure DevOps建立CI/CD流水线，部署耗时从2小时缩短至8分钟。
    3. 后端业务服务
    后端业务服务主要负责具体的业务逻辑处理。在该项目中，我们按业务划分为生产计划与调度模块、工艺管理模块、生产管理模块、质量管理模块、设备管理模块、物料管理模块、数据分析模块、用户管理模块、日志追溯模块和系统集成模块等微服务。这些微服务之间相互独立，仅在必要的时候才通过API网关进行交互，例如在执行批次创建的时候也需要调用物料管理模块的接口执行物料需求单的创建。如果涉及到分布式事务处理，还需要借助RabbitMQ和dotNetCore.CAP构件通过本地消息表与补偿事务机制的方式实现。后端业务服务部署在Kubernetes中，实现负载均衡和自动伸缩。
    系统在2023年10月顺利上线，截至到目前为止，该系统已先后推广到海内外8家工厂，取得显著成效。后续我们也在持续不断地对该系统进行不断的优化更新，例如：使用文生sql技术对数据分析功能模块进行了更新，允许用户通过问答的方式自动生成sql查询语句并查询数据，但该功能目前在sql优化上有瑕疵，针对索引的使用，大小表的join顺序还有待进一步优化；利用深度学习+图形分析技术，对不良品的瑕疵点进行标注，减少质量检验人员的操作时间，提高生产效率。计划进一步探索AI技术在微服务开发中的应用，研究自然语言生成服务代码和数据库表的可能性，持续优化系统架构，提升开发效率和系统性能。