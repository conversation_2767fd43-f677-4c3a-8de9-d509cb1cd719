<?xml version="1.0" encoding="UTF-16LE"?>
<ModelingAuditTrailInfo>
    <HeaderId>001ccd8000000015</HeaderId>
    <CDOInstanceId>0004e28000000002</CDOInstanceId>
    <CDOTypeId>1250</CDOTypeId>
    <CDOTypeName>Factory</CDOTypeName>
    <DisplayName><![CDATA[正泰四期电池片]]></DisplayName>
    <Fields>
        <TrivialField>
            <FieldName>Name</FieldName>
            <DisplayName><![CDATA[Factory Name]]></DisplayName>
            <DefaultDisplayName><![CDATA[Factory Name]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>4</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[]]></OldValue>
                    <NewValue><![CDATA[正泰四期电池片]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>IsFrozen</FieldName>
            <DisplayName><![CDATA[Locked/Unlocked]]></DisplayName>
            <DefaultDisplayName><![CDATA[Locked/Unlocked]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>RequireLocation</FieldName>
            <DisplayName><![CDATA[Require Location]]></DisplayName>
            <DefaultDisplayName><![CDATA[Require Location]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <SubentityField>
            <FieldName>WIPMsgDefMgr</FieldName>
            <DisplayName><![CDATA[WIP Msg Def Mgr]]></DisplayName>
            <DefaultDisplayName><![CDATA[WIP Msg Def Mgr]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldValues>
                <Subentity>
                    <Action>Create</Action>
                    <DisplayName><![CDATA[WIP Msg Def in 正泰四期电池片]]></DisplayName>
                    <HeaderId>001ccd8000000015</HeaderId>
                    <CDOInstanceId>00049c8000000002</CDOInstanceId>
                </Subentity>
            </FieldValues>
        </SubentityField>
        <TrivialField>
            <FieldName>DisplayGeneralMessage</FieldName>
            <DisplayName><![CDATA[Display Message]]></DisplayName>
            <DefaultDisplayName><![CDATA[Display Message]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>AllowLineSettingsOverride</FieldName>
            <DisplayName><![CDATA[Allow Terminal Line Assignment Override]]></DisplayName>
            <DefaultDisplayName><![CDATA[Allow Terminal Line Assignment Override]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>ReprintReasonRequired</FieldName>
            <DisplayName><![CDATA[Reprint Reason Required]]></DisplayName>
            <DefaultDisplayName><![CDATA[Reprint Reason Required]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>jaCarrierQty</FieldName>
            <DisplayName><![CDATA[CarrierQty]]></DisplayName>
            <DefaultDisplayName><![CDATA[CarrierQty]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>9</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[]]></OldValue>
                    <NewValue><![CDATA[100]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>XMLPort</FieldName>
            <DisplayName><![CDATA[XML Port]]></DisplayName>
            <DefaultDisplayName><![CDATA[XML Port]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>1</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[2881]]></OldValue>
                    <NewValue><![CDATA[443]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>XMLServerName</FieldName>
            <DisplayName><![CDATA[XML Server Name]]></DisplayName>
            <DefaultDisplayName><![CDATA[XML Server Name]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>4</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[localhost]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>WebLoginTimeLimit</FieldName>
            <DisplayName><![CDATA[Web Login Time Limit (Days)]]></DisplayName>
            <DefaultDisplayName><![CDATA[Web Login Time Limit (Days)]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>2</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[0]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>scsPreactorEnabled</FieldName>
            <DisplayName><![CDATA[Is Preactor Enabled (Required IS)]]></DisplayName>
            <DefaultDisplayName><![CDATA[Is Preactor Enabled (Required IS)]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>scsQuantixEnabled</FieldName>
            <DisplayName><![CDATA[Quantix Enabled]]></DisplayName>
            <DefaultDisplayName><![CDATA[Quantix Enabled]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>scsEnabledHybridHPE</FieldName>
            <DisplayName><![CDATA[Hybrid HPE Enabled]]></DisplayName>
            <DefaultDisplayName><![CDATA[Hybrid HPE Enabled]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
        <TrivialField>
            <FieldName>scsUnloadCarrierContainer</FieldName>
            <DisplayName><![CDATA[Unload Carrier Container]]></DisplayName>
            <DefaultDisplayName><![CDATA[Unload Carrier Container]]></DefaultDisplayName>
            <IsList>false</IsList>
            <Action>Create</Action>
            <FieldType>7</FieldType>
            <FieldValues>
                <Value>
                    <OldValue><![CDATA[0]]></OldValue>
                    <NewValue><![CDATA[false]]></NewValue>
                    <Action><![CDATA[Create]]></Action>
                </Value>
            </FieldValues>
        </TrivialField>
    </Fields>
</ModelingAuditTrailInfo>