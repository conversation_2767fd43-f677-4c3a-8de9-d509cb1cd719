<!--
 * @Author: WDD
 * @Date: 2025-05-17 10:40:04
 * @LastEditors: WDD
 * @LastEditTime: 2025-05-20 10:23:34
 * @Description: file content
-->
## 简介

2023年4月，为提高企业综合竞争力，我司正式启动了以制造执行系统MES为基石的数字化建设项目。作为系统架构师，我主导了本项目的整体架构设计和实施工作。本文以该项目为蓝本，深入探讨了软件架构评估方法在项目中的实施过程。通过云原生架构结合Kubernetes的弹性伸缩容保障主服务的性能；通过管道过滤器架构风格实现用户的身份验证和权限验证，保障系统的安全性；通过低代码平台快速完成需求功能的快速开发，保障了系统的可修改性。我采用ATAM的4个评估活动对每个场景进行质量属性的分析和折中，并获得不错效果。该项目于2023年10月正式上线，并逐步覆盖海内外8家生产基地，成效斐然。

## 正文

在制造业数字化的浪潮下，某省2022年工业互联网报告指出：73%的制造业因传统MES架构僵化，跨厂区协同效率低于业内平均水平。这些MES系统多采用单体架构，灵活性差，业务扩展困难，硬件成本达42%，难以实现柔性化生产，形成信息孤岛，系统整合困难，阻碍企业数字化转型。

为应对生产数据实时采集能力不足、跨厂区协同效率低下的问题，我司于2023年4月正式启动了新一代云原生MES系统建设，旨在通过架构革新实现制造生产全流程的敏捷响应。该项目采用三阶段迭代开发模式，首次投入800万，覆盖1个生产基地，5个厂区，接入1000+台工业设备。MES系统是生产制造的重要工具，它具有生产计划动态调度、生产数据实时采集、工艺流程控制、质量控制、物料追溯和设备状态管理等功能，打通了企业计划层和制造现场的信息壁垒，实现生产的透明化和柔性化。作为系统架构师，我主导本项目的整体架构设计和实施，基于业务边界将系统细分为10大微服务，保障系统的99.9999%可用性。截至到2025年3月，该系统已成功上线了10个微服务，单生产基地日产量达200MW，跨厂区协同效率提升68%。

软件架构评估是为保证架构设计的合理性、完整性和针对性，保证系统质量，降低成本与投资成本，对设计好的系统架构进行评估分析，目前一般常用SAAM和ATAM两种评估方法。这两种软件架构评估方法在评估目标、质量属性和评估活动等方面有以下区别。1. SAAM通过描述应用程序属性的文档，来验证基本体系架构假设和原则，不仅能评估体系架构对特殊需求的适应能力，也能比较不同的体系架构；ATAM 是在SAAM基础上发展而来，在考虑多个相互影响的质量属性的情况下，从原则上提供一种理解体系架构能力的方法，使用该方法确定在多个质量属性之间折中的必要性。2. SAAM一般用来分析体系架构的可修改性、可移植性和可扩展性；ATAM则多用来分析系统架构的性能、安全性、可修改性和实用性。3. SAAM有场景开发、体系结构描述、单个场景开发、场景互动和总体评估5个评估活动；ATAM有4个主要活动，有场景和需求收集、体系结构视图和场景实现、属性模型构造和分析、折中。

在该项目中，我采用ATAM对云原生架构进行评估，并从性能、安全性和可修改性三个场景详细介绍架构评估的实施过程和效果。

1.	性能场景评估
传统MES系统中采用单体架构，模块间相互耦合，难以水平扩展，向上扩展时硬件成本高昂，服务器硬件性能成了系统性能的瓶颈，随着业务的增长，核心服务的响应时间平均达500ms，部分功能的响应时间更是长达几分钟。

2.	安全性场景评估
3.	可修改性场景评估

2023年10月，该系统已成功上线，并逐步覆盖海内外8个生产基地，取得了令人瞩目的成效：部署时间从之前的2小时缩短到8分钟、核心服务TP99<300ms、服务可用性达99.9999%、服务响应时间提升60%、硬件成本降低40%。ATAM架构评估方法在该项目中的实施证明，合理科学的运用评估活动可以有效提高系统性能、安全性、可用性和维护性。

展望未来，我们持续不断地对系统进行优化升级，例如使用文生SQL技术革新数据分析模块，用户可通过自然语言提问的方式自动生成查询语句和数据报表，大大提高了查询速度和报表开发效率。但该功能在索引利用和大小表连接方面仍有优化空间。同时，我们还通过深度学习结合图像分析技术，对不良品瑕疵点进行精准标注，提升了质检人员的工作效率，减轻了工作负担。后续，我们将继续发掘AI技术在云原生MES系统中的应用潜力，研究结合低代码开发平台通过自然语言生成前端界面、业务代码和数据库表的可能性，以期待持续优化系统架构，提升开发效率于系统性能，为企业数字化转型提供助力。
