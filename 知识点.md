<!--
 * @Author: WDD
 * @Date: 2025-04-23 19:09:39
 * @LastEditors: WDD
 * @LastEditTime: 2025-05-20 10:07:55
 * @Description: file content
-->

瀑布模型：将软件生命周期中的每个活动规定为依线性顺序执行的各个阶段的模型，这些模型包括需求分析，设计、编码，运行和维护。 容易理解，管理成本低，每个阶段都有对应的成果和产物，各个阶段有明显的界限划分和顺序要求，一旦发生错误，可以推倒重来，适用于需求明确的项目，一般表述为需求明确，或二次开发，或对数据处理类型的项目。

原型：需求不明确，帮助用户确定需求。

螺旋模型：引入了风险分析，结合了瀑布模型和演化模型的优点，它是由制定计划，风险分析，实施工程和客户评估这一循环组成，它最初由概念项目开始第一个螺旋。

V模型：强调测试贯穿项目始终，而不是集中在测试阶段，是一种测试的开发模型。

快速开发RAD:1. 快速应用开发模型，是瀑布模型的一种高速变种，适用于比传统生命周期快的多的开发方法，它强调极端的开发周期，通常适用基于构件的开发方法获得快速开发。 过程：业务建模，数据建模，过程建模，应用生成，测试与交付。 适用性：RAD对模块化要求比较高，如果某项

统一过程，用例驱动，以架构为核心，迭代和增量,  初始，细化（架构确定），构造，移交  9个核心工作流：业务建模，需求，分析与设计，实现，测试，部署， 配置与变更管理，项目管理，环境  适用性：适用于需求变化不大，对时间有要求的项目。

特征驱动开发：6个关键角色：项目经理、首席架构、开发经理、主程序员、程序员和领域专家， 3要素：人，过程和技术

需求开发：需求获取，需求分析，形成需求规格，需求确认与验证
需求管理：基线的管理，变更管理，版本管理，需求跟踪，需求状态跟踪，

系统分析与设计
结构化方法：数据字典，功能模型（DFD）,行为模型（状态转换图），数据模型（ER图），概要设计（确定每个模块的功能和调用管理，形成模块结构图），详细设计（为每个具体任务选择技术手段和处理方法），
内聚：功，顺（先后执行），通（使用同一数据或输出同一数据），过（严格的执行顺序），时（同时执行）逻（逻辑上具有相似的处理动作）偶；非数（传递数据参数）标（参数表、数据结构）控（参数中有控制信息）外（全局简单变量）公（公共数据区域）内（内部信息）
面向对象方法：

面向对象的概念类：
边界类：用于封装在用例内、外的信息或数据流。位于系统与外界的交接处，包括窗体、报表、打印机和外部系统接口
实体类：映射需求中的每个实体，实体中保存需要存储在永久存储体中的信息
控制类：用于控制用例工作的类，

用例规约：用例名，用例ID，角色，前置条件，后置条件，主事件流，备选事件流，非功能需求，特殊需求，优先级，扩展点，

用例关系：
包含：两个或两个以上的用例中提取出公共行为作为新的用例，这个新用例称为抽象用例，原始用例称为基本用例；
泛化：当多个用例共同拥有类似的结构和行为时，可以将这种共性抽象成父用例，原始用例作为泛化关系中的子用例，子用例继承了父用例所有的结构、行为和关系；
扩展：如果一个用例明显混合了两种或两种以上的场景，即情况可能发生多种分支，则可以将这个用例分为一个基本用例和多个扩展用例
只有包含用例，是箭头指向被包含用例


| 架构风格|子风格| 优点|缺点|
---|----|-----|---
| 数据流风格 | 批处理、管道-过滤器  |  1. 分步处理，易于理解和维护。<br>2. 适用于数据处理任务。<br>3. 可以并行处理数据。<br>4. 可以重复使用组件。<br>5. 可以方便地添加新组件。<br>6. 良好的维护性。<br> 7.松耦合  | 1. 交互性差。<br>2. 不适合实时处理任务。<br>3. 可能存在数据瓶颈,性能差。 |
| 调用/返回风格 | 主/子程序、面向对象、分层架构 | 1. 易于理解和维护。<br>2. 适用于业务逻辑处理任务。<br>3. 可以重复使用组件。<br>4. 可以方便地添加新组件。<br>5. 良好的维护性。<br>6. 松耦合 | 1. 交互性差。<br>2. 不适合实时处理任务。<br>3. 可能存在数据瓶颈,性能差。 |
| 独立构件风格 | 进程通信、事件驱动 | 1. 高度并行。<br>2. 适用于实时处理任务。<br>3. 可以重复使用组件。<br>4. 可以方便地添加新组件。<br>5. 良好的维护性。<br>6. 松耦合 | 1. 交互性差。<br>2. 不适合数据处理任务。<br>3. 可能存在数据瓶颈,性能差。 |
| 虚拟机风格 | 解释器、规则系统 | 1. 易于理解和维护。<br>2. 适用于业务逻辑处理任务。<br>3. 可以重复使用组件。<br>4. 可以方便地添加新组件。<br>5. 良好的维护性。<br>6. 松耦合 | 1. 交互性差。<br>2. 不适合实时处理任务。<br>3. �风格 | 解释器、基于规则的系统 | 1. 高度灵活。<br>2. 适用于需要动态变化的任务。<br>3. 可以重复使用组件。<br>4. 可以方便地添加新组件。<br>5. 良好的维护性。<br>6. 松耦合 | 1. 交互性差。<br>2. 不适合实时处理任务。<br>3. 可能存在数据瓶颈,性能差。 |
| 以数据为中心的风格 | 数据库、黑板、超文本 | 1. 高度可扩展。<br>2. 适用于需要大量数据处理的任务。<br>3. 可以重复使用组件。<br>4. 可以方便地添加新组件。<br>5. 良好的维护性。<br>6. 松耦合 | 1. 交互性差。<br>2. 不适合实时处理任务。<br>3. 可能存在数据瓶颈,性能差。 |


状态图描述的是对象状态与事件之间的关系，强调实体在事件反应下的动态行为，适用于描述不同用例之间的对象行为。状态的变化通常依赖事件的触发
活动图描述不同对象实体顺序执行所遵循的规则，重点描述系统行为，强调活动变化的顺序和条件控制；支持并行执行；一个活动结束后立即进入下一个活动；面向对象的；
流程图描述的是功能执行的顺序，强调处理过程；一般限于顺序过程； 面向过程的
顺序图是一种交互图，它强调对象之间消息发送的顺序，同时显示对象之间的交互。强调按时间顺序。

云原生架构是基于云原生技术的一组架构原则和设计模式的集合，旨在将云应用中的非业务代码部分进行最大化的剥离，从而让云设施接管应用中原有的大量非功能特性，使业务不再有非功能性业务中断困扰的同时，具有轻量，敏捷、高度自动化的特点，由于云原生是面向“云”而设计的应用，因此，技术部分依赖于传统云计算的3层概念，即Iaas,Paas,SaaS。具有弹性，服务化，自动化、可观测、零信任原则，持续演化，韧性



微服务 | SOA
----|---
能拆分就拆分| 整体的，能放一起就放一起
纵向业务分割|水平层次分割
单一组织负责|按层级划分不同部门的组织负责
细粒度|粗粒度
两百字就能说明白|几百字只相当于目录
独立的子公司|类似大公司里面划分的一些业务单元（BU）
组件小|存在较复杂的组件
业务逻辑存在每个服务中|业务逻辑跨多个业务领域
使用轻量级通信，例如http|企业服务纵向ESB充当服务间通信的角色

![关系数据库与NoSql数据库](1747021390286.jpg)


逻辑维，时间维，知识维  霍尔三维

为达到整体最优，对系统的组成元素、组织结构、信息流和控制机构进行分析研究

TPS->MIS->DSS->ES->OAS->ERP
MIS 信息源，信息处理器，信息用户、信息管理者
DSS：1. 数据和模型是DSS的主要资源；2. 只能辅助用户决策而不能代替用户做决策；3. 提高决策有效性而不是提高决策效率；4. 主要用来解决非结构和半结构化问题
ES：知识表示+知识推理， 知识库，综合数据库（中间状态、中间结构、求解过程），推理机，知识获取，解释程序（面向用户服务的）


信息化的**主体**是全体社会成员（政府、企业、组织和个人），**时域**是一个长期过程，**空域**是经济和社会的一切领域，**手段**是先进社会生产工具

信息化需求：战略，运作，人才

战略规划：企业战略规划，信息化战略规划，信息系统战略规划：以数据处理为核心（企业系统规划法、关键成功因素法、战略目标集转化法），以企业MIS为核心（战略数据规划法、信息工程法、战略栅格法），以集成为核心（价值链分析法、战略一致性模型）

信息化三流：信息流，资金流，物流

数据挖掘：挖掘潜在的规律，关联分析，序列分析，分类分析，聚类分析、联机分析处理OLAP:多维分析，切片，钻取,
数据仓库：面向主题，集成的，稳定的，反映历史变化
数据湖：原始数据，无限扩展的，并行执行，可结构化或非结构化，既可事务处理，又可数据分析

企业数字化转型：初始->单元->流程->网络->生态

智能制造体系：设备层->单元->车间->企业->协同

企业应用集成层次：表示集成、数据集成、控制集成、业务流程集成
企业门户：企业网站、信息门户、知识门户、应用门户、垂直门户

结构化：自上而下，阶段产物、工程化文档化、强调整体性全局性、易理解，管理成本低
面向对象法：自下而上，更好的复用性、关键在于建立一个全面、合理、统一的模型，分析设计实现三个阶段界限不明显，更符合人的思维方式
SOA:粗粒度、松耦合、标准化，构件化、  三个抽象级别：操作->服务->业务流程

统一过程4个阶段：初始，细化，构建，交付   用例驱动、以架构为中心，迭代与增量，  业务建模，需求，分析与设计，实现，测试，部署，配置与变更管理，项目管理，环境

![设计模式](image.png)

体系演化步骤：需求变化归类、制定体系结构演化计划、修改增加删除构建、更新构件相互作用、构件组装和测试、技术评审

逆工程抽象层次：实现级（结构树、符号表、过程的设计模型）、结构级（分量之间依赖关系、程序和数据结构）、功能级（功能与程序段之间关系，对象模型，数据和控制流模型）、领域级（程序和应用领域概念之间关系，UML状态图和部署图）

4+1视图：逻辑视图、实现/开发视图、进程视图、部署/物理视图、用例视图/场景

软件架构重用的优点：提高开发效率、提高软件质量、降低开发成本、提高系统可扩展性和可维护性、促进团队协作的知识共享、降低技术风险

重构/重组：同一抽象级别上转化
设计恢复：已有程序抽象除数据设计、结构设计
逆向工程：更高抽象层次程序
正向工程：改变或重构现有系统
再工程：逆向+新需求+正向

需求分析：
结构化分析:数据字典，功能模型（DFD），行为模型(STD)，数据模型(ER)
面向对象分析:用例模型，分析模型，UML

需求开发：需求获取，分析，形成需求规格，需求确认和验证
需求管理：变更控制，版本控制，需求跟踪，需求状态跟踪

类图中关系语义强度，从低到高：依赖-》聚合-》组合-》继承

顺序图组合片段：Loop,Opt(选项),Alt(条件)

需求变更管理：识别出问题-》问题分析和变更描述-》变更分析和成本计算-》变更实现-》修改

面向对象的设计原则：单一职责，依赖倒置，接口隔离，最少知识（迪米特）、里氏替换、开闭、组合重用

场景：从风险承担者的角度与系统交互的简短描述
刺激源：某个生成该刺激的实体（人、系统内部、系统外部、时间）
刺激：该刺激是当刺激达到系统时所需要考虑的条件
环境：该刺激在某些条件内发生。当刺激发生时，系统可能处在运行、过载或其他情况。
制品：某个制品被激励。可能是系统（或系统的一部分）
响应：在激励到达后所采取的行动
响应度量：当响应发生时，应当能够以某种方式对其度量，以对需求进行测试。

##### 可靠性设计
1. 避错技术
1.1. 在系统正式运行之前避免、发现和改正错误
1.2. 技术评审、系统测试、正确性验证
2. 检错技术
2.1. 成本低于容错，但不能自动解决问题需要人工干预
2.2. 四要素
2.2.1 检测对象：检测点应该是容易出错的地方；检测内容具有代表性
2.2.2 检测延时
2.2.3 实现方式：判断返回结果是否超出正确范围，超出则异常处理；计算运行时间，超时则异常处理；通过状态标志位判断；
2.2.4 处理方式：查出故障-》停止运行-》报警
3. 容错技术
3.1. 冗余技术
3.1.1. 结构冗余：硬件、软件冗余
3.1.2. 信息冗余：校验码
3.1.3. 时间冗余：重复多次相同的计算
3.2. 软件容错技术
3.2.1. N版本设计：每个版本需求说明相同，但由不同的人来设计实现；实行多数表决机制；静态冗余；在传统软件开发过程，增加了相异成分规范评审、相异性确认、背对背测试
3.2.2. 恢复快设计：每个恢复快的功能相同，设计有差异，一个出故障，则替换为另一个；动态冗余
3.2.3. 防卫式程序设计：try-catch;输入参数校验
3.3. 双机容错技术
3.3.1. 双机热备：一台运行，一台待命
3.3.2. 双机互备：同时运行，互为备份
3.3.3. 双机双工：集群前身
3.4. 集群技术
4. 降低复杂度设计
4.1. 简化软件结构
4.2. 缩短程序代码长度
4.3. 优化软件数据流向

##### 性能：系统的响应能力，即要经过多长时间才能对某个事件做出响应，或者在某段时间内能处理事件的个数
1. 资源需求：提高计算效率，减少计算开销，管理事件率，控制采样频率
2. 资源管理：引入并发，维持多个副本，增加可用资源
3. 资源仲裁：资源调度策略，先进先出，固定优先级，动态优先级，静态调试

##### 可用性：系统能正常运行的时间比例。系统发生故障后能够快速完成故障的定位和处理，确保系统能继续正常运行的能力。
1. 错误检测：ping/Echo、心跳
2. 错误恢复：表决、冗余（主、被动）、备件
3. 错误预防：进程监视器、事务、从服务器删除

##### 安全性：指系统在向授权用户提供服务的同时拒绝非授权用户使用的企图或拒绝服务的能力
机密性、完整性、不可否认性、可控性
1. 抵抗攻击：身份认证、用户授权、数据加密、限制访问、限制暴露
2. 检测攻击：入侵检测
3. 从攻击中恢复：识别（审计追踪）、恢复（冗余）

##### 可修改性：
1. 局部化修改：维持语义一致性、预期期望的变更、泛化模块、限制可能的选择、抽象通用服务
2. 防止连锁反应：隐藏信息、维持现有接口、限制通信路径、使用仲裁者
3. 推迟绑定事件：运行时注册、配置文件、多态、组件更换、遵守已定义协议


软件维护：正确性维护、适应性、完善性、预防性维护

质量保证一般是每隔一段时间进行，主要是通过系统的**质量审计和过程分析**来保证项目质量。
质量控制是实时监控项目结果，以判断它们是否符合相关质量标准，制定有效方案，以消除产生质量问题的原因

数据治理3个目标：符合合规标准、管理数据安全风险、促成数据开发利用
数据治理4个流程：数据规划、数据采集、数据存储、数据应用

软件架构的复用：机会复用、系统复用

软件架构：为软件系统提供一个结构、行为和属性的高级抽象；干系人交流的手段
软件架构风格：描述系统组织方式和惯用模式，反映特定领域应用共有的结构和语义特征，强调架构设计复用，架构定义了一个词汇表和一组约束。

##### ABSD
1. 特点：架构驱动、4+1视图来描述架构、强调业务、质量和功能需求组合来驱动架构设计；使用用例来描述功能需求；使用场景来描述非功能需求；
2. 过程：
2.1. 架构需求：需求获取、表示构件、需求评审；
2.2. 架构设计：提出架构模型、映射构件、分析构件相互作用、产生架构、设计评审；
2.3. 架构文档化：使用者角度、必须分发给所有相干人员、必须保证开发者手上是最新的；体系架构说明书、质量设计说明书
2.4. 架构复审：标识潜在的风险，及早发现架构设计中的缺陷和错误；
2.5. 架构实现：分析与设计-》构件实现-》构件组装-》系统测试；
2.6. 架构演化：需求变动归类-》制定演化计划-》增删改构件-》更新构件的相互作用-》构件组装与测试-》技术评审


构件：
1. 软件构件是一种组装单元，它具有规范的接口规约和显示的语境依赖。软件构件可以独立部署并被第三方任意的组装。
2. 构件是系统中有价值的，几乎独立的，可以被替换的一个部分，它在良好的体系结构语境中满足某清晰的功能。
3. 构件是一个独立发布的功能部分，可以通过接口访问它的服务。

中间件优势：
1. 面向需求；
2. 业务的分割和包容性；
3. 设计与实现隔离；
4. 隔离复杂的系统资源
5. 符合标准的交互模型
6. 软件复用
7. 提供对应用构件的管理

SAAM：一种非功能质量属性的体系架构评估方法，通过描述应用程序属性的文档，来验证基本体系结构假设和原则。不仅能评估系统对特殊质量需求的适应性，也能比较不同的体系架构；针对可修改性、可移植性和可扩充性；5个步骤：场景开发、体系结构描述、单个场景评估、场景交互和总体评估
ATAM: 从SAAM的基础上发展而来，针对多种质量属性相互影响的情况下，从原则上提供一种理解软件体系结构能力的方法，通过该方法确定多个质量属性之间进行折中的必要性。
4个步骤：场景和需求收集、体系结构视图和场景实现、属性模型构造和分析、折中分析
九步法：介绍ATAM方法-》描述商业目标-》描述体系架构-》标识体系架构步骤-》产生质量属性树-》分析体系结构步骤-》讨论质量需求的次序（头脑风暴[用例场景、增长场景、探索性场景]）-》分析体系结构步骤-》提交结果

REST 5个原则：
1. 网络上所有事物都可以抽象为资源；
2. 所有资源都有唯一标识；
3. 对资源的操作不会改变资源标识；
4. 所有操作都是无状态的；
5. 通过通用连接件接口进行操作

边云协同分类：
1. 资源协同:边缘基础设施资源的调度
2. 数据协同：边缘采集数据并初步分析
3. 智能协同：云端做集中式模型训练，再将模型下发到边缘节点
4. 应用管理协同：边缘部署与运行，云端开发和测试
5. 业务管理协同：边缘提供应用实例，云端提供业务编排能力
6. 服务协同：均有SaaS能力

##### Redis
1. 分布式存储方案：主从，哨兵，集群
2. 集群切片方案：客户端切片；中间件切片；客户端服务端协作切片
3. 数据分片方案：范围分片；hash分片；一致性hash分片
4. 淘汰算法：noeviction, volatitle-random, volatitle-lru(最近未使用)，volatitle-ttl(ttl值小)，allkeys-randow,allkeys-lru
5. 持久化：RDB,AOF
6. 常见问题：缓存雪崩（锁或队列、不同的ttl，二级缓存）、缓存穿透（默认值、布隆过滤器）、缓存预热（手动、启动时加载、定时刷新）、缓存更新（定期清理）、缓存降级（保证主服务可用）

比较 |MemCache|Redis
---|---|---
数据类型| 简单key-value | key-value,hash,list,set,sorted set等丰富数据结构
持久性| 不支持 | 支持
分布式存储| 客户端/一致性hash分片| 支持，主从，哨兵，集群
事务支持|不支持|有限支持
内存管理|有，私有内存池|无
多线程支持|支持|Redis5.0版本之后支持

死锁四个条件：互斥、保持和等待、不可剥夺、环路等待

性能指标：
1. 计算机：字节和数据通道宽度、主存容量和存取速度、运算速度、吞吐量与吞吐率、响应时间与完成时间、兼容性
2. 路由器：设备吞吐量、端口吞吐量、全双工线速转发能力、丢包率、时延、时延抖动、VPN支持能力、端口硬件队列数、基于Web的管理、网管类型
3. 交换机：交换机类型、配置、支持的网络类型、最大ATM端口数、支持协议和标准
4. 网络：设备级性能指标；网络级性能指标；应用级性能指标；用户级性能指标；吞吐量
5. 操作系统：系统可靠性、吞吐量、响应时间、系统资源利用率、可移植性
6. 数据库管理系统：数据库的大小、数据库中表的数量、单个表的大小、表中允许的记录（行）数量、单个记录的大小、表上允许的索引数量、数据库上允许的索引数量、最大并发事务处理能力、负载均衡能力、最大连接数
7. web服务器：最大并发数、响应延迟、吞吐量；性能评测方法包括：基准测试、压力测试、可靠性测试

I/O控制方式CPU参与程度：程序查询>程序中断>DMA>通道

#### 信息安全
##### 5个基本要素
1. 机密性：不泄露给非授权的用户、实体或程序
2. 完整性：未经授权不得修改
3. 可用性：合法许可的用户能及时获取网络信息或服务
4. 可控性：可以控制授权范围内的信息流向和行为方式
5. 可审查性：对出现的安全问题提供调查的依据和手段
##### 安全隐患、漏洞
1. 物理安全性
2. 软件安全漏洞
3. 不兼容使用安全漏洞
4. 选择合适的安全哲理
##### 加解密技术
1. DES:56位
2. 3DES:112位（2个56位密钥）
3. IDEA:128位（64位明文、密文）
4. RSA:2048位（或1024位）
##### 抗攻击技术
1. 密钥的选择（算法的安全性在于密钥）：增大密钥空间、选择强钥、密钥随机性
2. 拒绝攻击：加强对数据包的特征识别、设置防火墙监视本地端口的使用情况、对通信数据量进行统计可能得到有关攻击系统的位置和数量信息、尽可能的修复已发现的问题和系统漏洞
3. ARP欺骗：固化ARP表、使用ARP服务器、采用双向绑定的方式、ARP防护软件
4. DNS欺骗：被动监听检测、虚假报文检测、交叉检查查询
5. IP欺骗：删除UNIX中所有的/etc/hosts.equiv、$Home/rhosts文件、修改/etc/inetd.conf文件，使得RPC机制无法应用；设置防火墙过滤来自外部而信源地址是内部IP的报文
##### 数字签名
1. 单向散列函数、不可逆、固定长度
2. 数字签名的用途是【防篡改】
3. 对数字签名进行私钥加密是【防抵赖】
##### 国产密码算法
1. 对称密码算法：SM1、SM4
2. 非对称密码算法：SM2
3. 杂凑算法：SM3
4. 数字签名算法/标识密码算法：SM9
##### 计算机系统安全保护等级
用（户）系（统）（国）安结访
##### WPDRRC:
预警，保护、检测、响应、恢复、反击；3大要素：人员、技术和策略
##### 区块链：
###### 特点
1. 去中心化（由于使用**分布式核算和存储**,不存在中心化的硬件或管理机构，任意节点都有相同的权利和义务）、
2. 自治性（区块链采用协商一致的**规范或协议**，使得整个系统中的任意节点都能在去信任的环境中自由安全的进行交易，使得对“人”的信任转换到对机器的信任，任何人为干预均不起作用）
3. 匿名性（由于节点之间的交换遵循**固定的算法**，其数据的交互是无需信任的，因此交易双方无需公开身份来让对方信任）
4. 开放性（除用户加密信息之外，其他交易信息都是公开的）
5. 不可篡改性（数据在多个节点中存储，至少要修改51%的数据才能进行篡改，同时还有其他安全机制，如私钥签名）
###### 核心技术
1. 分布式存储：将数据分散存储在多个地方的数据存储技术，而存储的数据可以在多个参与者之间共享，人人可以参与，有相同的权利，一起记录数据，起到共同存储的能力
2. 共识机制：因为区块链的分布式网络中，没有中央权威，因此，网络需要一个决策机制来促成交易者达成一致。而共识机制就是一种协调大家处理数据的机制
3. 智能合约：是一种旨在用信息化的方式传播、验证和执行合同的计算机协议。智能合约可以在没有第三方的情况下，也能进行可信的交易，而且这些交易可追踪且不可逆转。所以智能合约在系统中，主要起到数据的执行作用。
4. 密码学：是一种特殊的加密和解密技术，区块链系统中，应用到多种多样的密码学技术，例如：公钥私钥、数字签名、哈希算法等。以此来保证系统的数据安全，并且证明数据的归属。

#### 嵌入式
1. 特点：实时性强，微型化、代码质量高、专业化、可裁剪、可配置
2. 实时操作系统调度算法：时间片轮转、优先级调度算法、抢占式优先级算法（任务紧急程度）、单调速率（任务周期）、最低松弛度（任务紧急程度）、最早截至时间（任务截至时间）
3. 微内核相比宏内核的优点：内核小只有基本功能、安全性可靠性稳定性更好、可用于分布式系统、用户态与核心态频繁切换效率不如宏内核
4. 低功耗设计方法：软硬件协同设计、编译优化、中断代替查询、减少系统持续运行时间、电源管理

#### 负载均衡技术
负载均衡技术是一种通过合理分配网络流量或计算任务，将工作负载分散到多个服务器、网络链路或其他计算资源上的技术。其核心目标是优化资源使用效率、提升系统性能、增强可靠性和避免单点故障。
##### 静态算法
1. 随机算法
2. 轮询算法
3. 加权轮询算法
4. 源地址hash散列算法
5. 目标地址hash散列算法
##### 动态算法
1. 最小连接数算法
2. 加权最小连接数算法：考虑节点处理能力
3. 加权百分比
##### 应用层负载技术
1. http重定向
2. 反向代理服务器
##### 传输层负载技术
1. DNS域名解析负载均衡
2. 基于NAT的负载均衡
##### 硬件负载均衡
1. F5
##### 软件负载均衡
1. LVS
2. Nginx
3. HAproxy

#### 响应式web设计
1. 流式布局 flex
2. 弹性布局 vw,vh
3. 媒体查询 @media
4. 响应式图片 比例缩放，降低分辨率

### 计算机网络
#### 局域网
##### 局域网架构风格
1. 单核心
2. 双核心
3. 环形
4. 层次局域网架构
##### 局域网拓扑结构
1. 星状结构：网络中每个节点都是通过连接线与中心节点相连，传输数据也必须先通过中心节点；中心节点是控制中心，任意两个节点的通信都只需要两步；
优点：传输速度快、网络结构简单、建网容易、便于控制和管理
缺点：可靠性低；网络共享能力差；中心节点一旦故障，整个网络瘫痪
2. 树状结构：分级的集中式网络；
优点：网络成本低，结构简单；任意两个节点之间不产生回路，每个链路之间都支持双向传输，结点扩充方便、灵活，方便巡查链路路径
缺点：除叶结点及其相连的链路外，任何一个工作站或链路故障都会影响整个网络的正常运行
3. 总线结构：将各个结点设备与一根总线相连，网络中所有节点的信息传输都通过总线
缺点：作为数据通信必经的总线的负载能力是有限的，这是通信媒体本身的物理性能决定的；它的故障将影响网络中所有结点的通信
4. 环状结构：网络中各结点通过一条首位相连的通信链路连接起来，形成一个闭合环形结构网；网络中各结点设备的地位相同，信息按照固定方向单向流动，两个结点之间仅有一条通道，系统中无信道选择问题
缺点：网络不便扩充；系统响应延时长；信息传输效率较低；任意结点的故障将导致物理瘫痪
5. 网状结构：任意结点彼此之间仅有一条通信链路；任何结点故障不会影响其他结点之间的通信，高可用性
缺点：网络布线较为繁琐；建设成本高，控制方法复杂

##### 广域网
1. 属于多级网络，通常由骨干网、分布网、接入网组成
2. 典型结构风格：单核心、双核心、环形、半冗余、对等子域、层次子域广域网

##### 5G
1. 主要特征：服务化架构、**网络切片**

##### 软件定义网络SDN
1. 层次：应用平面、控制平面、数据平面

##### 存储网络结构
###### 磁盘阵列
1. Raid0: 性能最高，并行处理，无冗余，无法恢复
2. Raid1: 镜像结构，50%可用，可恢复
3. Raid01: 高效也可靠
4. Raid3: 奇偶校验并行传送，N+1模式，有固定的校验盘，坏一个盘可恢复
5. Raid5: 分布式奇偶校验的独立磁盘；N+1模式，无固定的校验盘，坏一个可恢复
6. Raid6: 两种存储的奇偶校验；N+2模式，无固定校验盘，坏两个可恢复

##### OSI七层模型
1. 应用层: TCP [POP3(110),SMTP(25),HTTP(80),FTP(20/21),Telnet(23)]、UDP [DNS(53,递归查询，迭代查询),DHCP(67，IP地址自动分配，租约默认8天)、TFTP(69)、SNMP(161)]
2. 表示层
3. 会话层
4. 传输层: TCP，UDP
5. 网络层：路由器，IP,ARP，RARP,ICMP,IGMP
6. 数据链路层：交换机
7. 物理层 ： 集线器，中继器

Internet网络核心采取**分组交换**
网络中检测冲突的最长时间决定以太网帧最小长度为64字节

##### 交换技术
1. 交换机功能：集线、中继、桥接、隔离冲突域
2. 基于交换原理，交换机是基于MAC地址识别，能完成封装转发数据包功能的网络设备
3. 交换机功能：转发路径学习；数据转发；数据泛洪；链路地址更新

##### 网络构建关键技术
1. SDN:控制平面、数据平面、转发规则一致性更新技术
2. IPv4与IPv6:  双协议栈（IPv6网络结点上具有IPv6协议栈和IPv4协议栈）、隧道技术（IPv4网络基础上实现IPv6的构建，ISATAP隧道，6to4隧道，4over6隧道）、网络地址翻译技术(将IPv4和IPv6分别看着内部地址和外部地址，或相反，以实现地址转换)

##### 层次网络技术
接入层：为局域网接入广域网，或终端用户访问网络提供接入能力，包括MAC认证、MAC过滤、计费管理、收集用户信息
汇聚层：将网络业务连接到接入层，执行与安全、流量负载、路由相关的策略，包括数据包过滤、广播域定义、寻找
核心层：提供不同区域或下层的高速连接和最优传送路径，包括数据传输、出口路由

##### 网络冗余设计
1. 备用路径
2. 负载分担：对备用路径方式的扩充，通过并行链路提供流量分担来提高性能，主要的实现方式是通过两个或两个以上网络接口和路径来同时传递流量；主备相同，用负载均衡，主备不同，使用策略路由

##### 网络安全设计
1. 防火墙部署
2. VPN技术（Virtual Private Network）
3. 访问控制技术
4. 网络安全隔离
5. 网络安全协议
6. 网络安全审计

#### 数据库提供
##### 数据库模式
1. 视图的优点：简化用户操作；用户能以多角度看待同一数据、对重构数据库提供一定程度的逻辑独立性、对机密数据提供安全保护

##### 分布式数据库
1. 特点：数据独立性（分布透明性）、集中与自治共享结合的控制结构（局部自治、集中控制）、适当增加数据冗余度（提高可用性和可靠性、性能）、全局一致性、可串行性和可恢复性
2. 透明性：分片透明性（水平分片、垂直分片、混合分片）、位置透明性、复制透明性、局部映像透明性（逻辑透明、不必关系数据库类型、操作语言）
##### 分布式事务
1. 2PC：两阶段提交，表决和执行
 1.1. 优点：强一致性，
 1.2. 缺点：同步阻塞、单点故障、数据不一致风险；
 1.3. 适用：可用性要求不高、传统数据库
2. 3PC：引入超时机制，准备阶段拆分为CanCommit和PreCommit, 
优点：减少阻塞时间；
缺点：实现复杂，仍然可能数据不一致；
适用：可能要求稍高
3. TCC：Try(预留资源)、Confirm(确认)、Cancel(取消)；
优点：最终一致性；
缺点:代码侵入性强，需设计补偿机制；
适用：金融业务、订单支付等高一致性要求业务
4. 本地消息表：应用启动一个数据库事务，更新业务实体状态，并在事件表中写入一个事件；一个独立的消息发布线程或进程查询这个事件表，将事件发布到消息代理，并标准该事件为已发布；
优点：简单，依赖数据库事务；
缺点：消息重复，需消息者幂等；
适用：异步通知、日志记录等最终一致性场景
5. Saga：编排式：服务间通过事件通信，自主协调；编配式：中央协调器调度流程；
优点：适合长事务，松耦合；
缺点：补偿逻辑复杂，需保证幂等；
适用：业务流程复杂、跨多个服务的长事务
6. 基于消息队列的最终一致性：适用事务消息，保证消息可靠传递；
优点：高可用，解耦；
缺点：需保证消息幂等，存在延迟；
适用：异步处理，如库存扣减后通知物流
7. AT模式（Seata框架）：自动生成反向SQL，实现无侵入补偿；
优点：低侵入，类似2PC但更高效；
缺点：依赖框架，锁竞争可能；
适用：微服务架构下，跨库事务

##### 数据库设计过程
1. 需求分析: 产出物：数据字典、数据流图、需求规格说明书
2. 概念结构设计：ER图， 集成冲突：属性冲突，命名冲突，结构冲突
3. 逻辑结构设计：关系模型（数据模型三要素：数据结构、数据操作、数据约束条件），完整性约束（实体完整性、参照完整性、用户定义完整性）、范式（1NF（属性不可分）、2NF（完全依赖）、3NF（直接依赖）、BCNF（主属性内部依赖）、4NF（多值依赖））、反规范化 [1. 增加派生列 2. 增加冗余列 3. 重新组表 4. 表分割；优点：连接少，查询快，统计快，需要查的表减少，检索容易；缺点：数据冗余；数据不一致（触发器同步，应用程序同步，批处理）；增删改性能下降；逻辑代码实现困难]
4. 物理结构设计

##### 数据控制能力
1. 并发控制：ACID, 丢失更新（一级封锁协议,X锁），脏读（二级封锁协议，S锁）、不可重复读（三级封锁协议，S锁）
2. 完整性：实体完整性约束、参照完整性约束、用户定义完整性约束
3. 安全性：用户标识与鉴定、存取控制（授权）、密码存储与传输、视图保护、审计
4. 故障恢复：数据备份（完全备份、差量备份、增量备份）、故障恢复（事务故障、系统故障、介质故障）

##### 分区
1. 分区策略：

分区模式|分区依据|适合数据|数据管理能力|数据分布
--|--|--|--|--
范围分区|属性取值范围|周期性数据|能力强|不均匀
哈希分区|属性的哈希值|静态数据|能力弱|均匀
列表分区|属性的离散值|单属性、离散值|能力强|不均匀
组合分区|属性组合分区|周期、离散值|能力强|--

2. 分区的优点
2.1. 相对于单个文件系统或是硬盘，分区可以存储更多的数据
2.2. 数据管理方便，如果需要清除某年数据，直接删除该日期的分区即可
2.3. 精准定位分区查询数据，不需要全表扫描查询，大大提高查询效率
2.4. 可跨多个分区磁盘进行查询，来提高查询的吞吐量
2.5. 在涉及聚合函数查询时，可以很容易的进行数据合并

![NoSql数据库类型](image-1.png)

容器技术是一种内核轻量级的操作系统层虚拟化技术，能隔离进程和资源。虚拟机技术是一种创建于物理硬件系统，充当虚拟计算机系统的虚拟环境，该虚拟机可以独立运行在完全隔离的环境中，像本地计算一样运行计算机的程序。
容器化技术是一种将应用程序及其依赖环境（如代码、运行时、库、配置文件等）打包成一个独立、轻量级、可移植的单元（称为容器）的技术。